-- Sample Lua <PERSON>t for Testing Upload Functionality
-- Project Madara - Test Script v1.0.0

local function printWithStyle(message, style)
    style = style or "info"
    local timestamp = os.date("%H:%M:%S")
    
    if style == "success" then
        print(string.format("[%s] ✅ SUCCESS: %s", timestamp, message))
    elseif style == "error" then
        print(string.format("[%s] ❌ ERROR: %s", timestamp, message))
    elseif style == "warning" then
        print(string.format("[%s] ⚠️  WARNING: %s", timestamp, message))
    else
        print(string.format("[%s] ℹ️  INFO: %s", timestamp, message))
    end
end

local function checkEnvironment()
    printWithStyle("Checking script environment...", "info")
    
    -- Check if we're in Roblox
    if game then
        printWithStyle("Roblox environment detected", "success")
        printWithStyle("Game ID: " .. tostring(game.GameId), "info")
        printWithStyle("Place ID: " .. tostring(game.PlaceId), "info")
        return true
    else
        printWithStyle("Not running in Roblox environment", "warning")
        return false
    end
end

local function testBasicFunctionality()
    printWithStyle("Testing basic script functionality...", "info")
    
    -- Test basic Lua operations
    local testNumber = 42
    local testString = "Hello, Project Madara!"
    local testTable = {a = 1, b = 2, c = 3}
    
    printWithStyle("Number test: " .. tostring(testNumber), "success")
    printWithStyle("String test: " .. testString, "success")
    printWithStyle("Table test: " .. tostring(#testTable) .. " elements", "success")
    
    -- Test function creation
    local function add(a, b)
        return a + b
    end
    
    local result = add(5, 3)
    printWithStyle("Function test: 5 + 3 = " .. tostring(result), "success")
end

local function testRobloxFeatures()
    if not game then
        printWithStyle("Skipping Roblox features test - not in Roblox", "warning")
        return
    end
    
    printWithStyle("Testing Roblox-specific features...", "info")
    
    -- Test workspace access
    if game.Workspace then
        printWithStyle("Workspace access: OK", "success")
        
        -- Count parts in workspace
        local partCount = 0
        for _, obj in pairs(game.Workspace:GetChildren()) do
            if obj:IsA("Part") then
                partCount = partCount + 1
            end
        end
        printWithStyle("Parts in workspace: " .. tostring(partCount), "info")
    else
        printWithStyle("Cannot access Workspace", "error")
    end
    
    -- Test Players service
    if game:GetService("Players") then
        local Players = game:GetService("Players")
        local playerCount = #Players:GetPlayers()
        printWithStyle("Players service access: OK", "success")
        printWithStyle("Current players: " .. tostring(playerCount), "info")
        
        if Players.LocalPlayer then
            printWithStyle("Local player: " .. tostring(Players.LocalPlayer.Name), "info")
        end
    else
        printWithStyle("Cannot access Players service", "error")
    end
end

local function main()
    printWithStyle("=== Project Madara Test Script Started ===", "info")
    printWithStyle("Script uploaded and executed successfully!", "success")
    
    -- Run tests
    local envOk = checkEnvironment()
    testBasicFunctionality()
    
    if envOk then
        testRobloxFeatures()
    end
    
    printWithStyle("=== Test Script Completed ===", "success")
    
    -- Wait a moment to show results
    if game then
        wait(2)
        printWithStyle("Script will continue running...", "info")
    end
end

-- Handle script errors gracefully
local function safeExecute()
    local success, error = pcall(main)
    
    if not success then
        printWithStyle("Script execution failed: " .. tostring(error), "error")
    end
end

-- Execute the script
safeExecute()

-- Keep script alive if needed
if game then
    game:GetService("RunService").Heartbeat:Connect(function()
        -- Heartbeat connection to keep script running
    end)
end