-- Kill Switch Protected Key Loader
-- This script integrates with the kill switch system for remote script control

local SCRIPT_ID = "main-script-001" -- Change this for each script
local API_BASE_URL = "https://projectmadara.com/.netlify/functions/app"

-- Security validation
local function validateEnvironment()
    if getgenv().keyBypassed or getgenv().accessGranted or _G.keyBypassed then
        return false
    end
    
    local originalHttpGet = game.HttpGet
    if game.HttpGet ~= originalHttpGet then
        return false
    end
    
    return true
end

-- Make HTTP request with error handling
local function makeHttpRequest(url, method, data)
    local success, result = pcall(function()
        if method == "POST" and data then
            return game:GetService("HttpService"):PostAsync(url, data, Enum.HttpContentType.ApplicationJson)
        else
            return game:HttpGet(url)
        end
    end)
    
    if not success then
        warn("Network error: " .. tostring(result))
        return nil
    end
    
    return result
end

-- Parse JSON response
local function parseJSON(jsonString)
    local success, data = pcall(function()
        return game:GetService("HttpService"):JSONDecode(jsonString)
    end)
    
    if not success then
        warn("JSON parse error: " .. tostring(data))
        return nil
    end
    
    return data
end

-- Check script authorization status
local function checkScriptStatus(scriptId, hwid, key)
    if not validateEnvironment() then
        warn("Security violation detected")
        return { allowed = false, reason = "Security violation detected" }
    end

    local url = API_BASE_URL .. "?action=check-script&script_id=" .. 
               tostring(scriptId) .. "&hwid=" .. tostring(hwid)
    
    if key then
        url = url .. "&key=" .. tostring(key)
    end

    local response = makeHttpRequest(url)
    if not response then
        return { allowed = false, reason = "Unable to verify script status. Please check your connection." }
    end

    local data = parseJSON(response)
    if not data then
        return { allowed = false, reason = "Invalid server response" }
    end

    return data
end

-- Send heartbeat to server
local function sendHeartbeat(scriptId, hwid, key, sessionToken)
    if not validateEnvironment() then
        return { continue = false, reason = "Security violation" }
    end

    local heartbeatData = {
        script_id = scriptId,
        hwid = hwid,
        key = key,
        session_token = sessionToken
    }

    local jsonData = game:GetService("HttpService"):JSONEncode(heartbeatData)
    
    local response = makeHttpRequest(API_BASE_URL .. "/script-heartbeat", "POST", jsonData)
    if not response then
        return { continue = false, reason = "Network error during heartbeat" }
    end

    local data = parseJSON(response)
    return data or { continue = false, reason = "Invalid heartbeat response" }
end

-- Create kill switch protected script wrapper
local function createKillSwitchProtectedScript(scriptId, userFunction)
    return function()
        -- Get hardware ID
        local hwid = game:GetService("RbxAnalyticsService"):GetClientId()
        
        -- Generate session token
        local sessionToken = tostring(math.random(100000, 999999)) .. "_" .. tostring(tick())
        
        -- Check script status before execution
        print("🔒 Checking script authorization...")
        local statusCheck = checkScriptStatus(scriptId, hwid, nil)
        
        if not statusCheck.allowed then
            warn("❌ Script execution blocked: " .. (statusCheck.reason or "Unknown reason"))
            return
        end

        print("✅ Script authorized. Status: " .. (statusCheck.status or "active"))
        
        -- Set up heartbeat system
        local heartbeatInterval = tonumber(statusCheck.heartbeat_interval) or 30
        local isRunning = true
        
        -- Store in global environment to prevent bypassing
        getgenv().scriptRunning = true
        getgenv().killSwitchActive = true
        getgenv().currentScriptId = scriptId
        
        -- Heartbeat monitoring coroutine
        local heartbeatCoroutine = coroutine.create(function()
            while isRunning and getgenv().killSwitchActive do
                wait(heartbeatInterval)
                
                if not isRunning or not getgenv().killSwitchActive then
                    break
                end
                
                local heartbeat = sendHeartbeat(scriptId, hwid, nil, sessionToken)
                
                if not heartbeat.continue then
                    print("🛑 Kill switch activated: " .. (heartbeat.reason or "Script terminated by admin"))
                    isRunning = false
                    getgenv().scriptRunning = false
                    getgenv().killSwitchActive = false
                    break
                end
                
                -- Update heartbeat interval if changed
                if heartbeat.next_check then
                    heartbeatInterval = heartbeat.next_check
                end
            end
        end)
        
        -- Start heartbeat monitoring
        coroutine.resume(heartbeatCoroutine)
        
        -- Wrap user function with kill switch monitoring
        local protectedFunction = function()
            local success, error = pcall(function()
                if userFunction and type(userFunction) == "function" then
                    userFunction()
                else
                    -- Default behavior: load the key system
                    local keyUI = loadstring(game:HttpGet(API_BASE_URL:gsub("/.netlify/functions/app", "") .. "/Lua/key.lua"))()
                    if keyUI then
                        keyUI(function()
                            print("🎉 Script loaded successfully with kill switch protection!")
                            -- Your main script logic would go here
                            print("Main script is now running...")
                            
                            -- Example of continuous monitoring
                            spawn(function()
                                while getgenv().scriptRunning and getgenv().killSwitchActive do
                                    -- Your script's main loop
                                    wait(1)
                                    print("Script is running... (Kill switch active)")
                                end
                                print("Script execution stopped by kill switch")
                            end)
                        end)
                    end
                end
            end)
            
            if not success then
                warn("Protected script error: " .. tostring(error))
            end
            
            -- Cleanup
            isRunning = false
            getgenv().scriptRunning = false
            getgenv().killSwitchActive = false
            print("Script execution completed")
        end
        
        -- Execute the protected function
        spawn(protectedFunction)
    end
end

-- Anti-tamper protection
local function setupAntiTamper()
    local originalGetgenv = getgenv
    local originalGame = game
    
    -- Monitor for bypass attempts
    spawn(function()
        while true do
            wait(5)
            
            -- Check for bypass flags
            if getgenv().keyBypassed or _G.keyBypassed then
                warn("🚨 Bypass attempt detected! Script terminated.")
                getgenv().scriptRunning = false
                getgenv().killSwitchActive = false
                break
            end
            
            -- Check if global environment was tampered with
            if getgenv ~= originalGetgenv or game ~= originalGame then
                warn("🚨 Environment tampering detected! Script terminated.")
                getgenv().scriptRunning = false
                getgenv().killSwitchActive = false
                break
            end
        end
    end)
end

-- Main execution
local function main()
    if not validateEnvironment() then
        warn("❌ Security check failed. Please restart the script.")
        return
    end
    
    -- Setup anti-tamper protection
    setupAntiTamper()
    
    -- Create and execute kill switch protected script
    local protectedScript = createKillSwitchProtectedScript(SCRIPT_ID, nil)
    protectedScript()
end

-- Execute main function
main()

-- Return the protection functions for external use
return {
    createKillSwitchProtectedScript = createKillSwitchProtectedScript,
    checkScriptStatus = checkScriptStatus,
    validateEnvironment = validateEnvironment,
    SCRIPT_ID = SCRIPT_ID
}