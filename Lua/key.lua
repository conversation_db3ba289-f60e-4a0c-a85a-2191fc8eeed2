return function(correctFunction)
    local function validateEnvironment()
        if getgenv().keyBypassed or getgenv().accessGranted or _G.keyBypassed then
            return false
        end
        local originalHttpGet = game.HttpGet
        if game.HttpGet ~= originalHttpGet then
            return false
        end
        return true
    end

    local function makeHttpRequest(url)
        local success, result = pcall(function()
            return game:HttpGet(url)
        end)
        
        if not success then
            warn("Network error: " .. tostring(result))
            return nil
        end
        
        return result
    end

    local function parseJSON(jsonString)
        local success, data = pcall(function()
            return game:GetService("HttpService"):JSONDecode(jsonString)
        end)
        
        if not success then
            warn("JSON parse error: " .. tostring(data))
            return nil
        end
        
        return data
    end

    local function checkScriptStatus(scriptId, hwid, key)
        if not validateEnvironment() then
            warn("Security violation detected")
            return { allowed = false, reason = "Security violation detected" }
        end

        local url = "https://projectmadara.com/.netlify/functions/app?action=check-script&script_id=" .. 
                   tostring(scriptId) .. "&hwid=" .. tostring(hwid)
        
        if key then
            url = url .. "&key=" .. tostring(key)
        end

        local response = makeHttpRequest(url)
        if not response then
            return { allowed = false, reason = "Unable to verify script status. Please check your connection." }
        end

        local data = parseJSON(response)
        if not data then
            return { allowed = false, reason = "Invalid server response" }
        end

        return data
    end

    local function checkKey(key)
        if not validateEnvironment() then
            warn("Security violation detected")
            return false
        end

        assert(key and #key > 0, "A valid key is required")

        if not key:match("^PL%-[A-F0-9%-]+$") then
            warn("Invalid key format")
            return false
        end

        local hwid = game:GetService("RbxAnalyticsService"):GetClientId()
        local url = "https://projectmadara.com/.netlify/functions/app?action=check-key&key=" .. key .. "&hwid=" .. hwid

        local response = makeHttpRequest(url)
        if not response then
            warn("Network error: Unable to validate key")
            return false
        end

        local data = parseJSON(response)
        if not data then
            warn("Invalid server response")
            return false
        end

        if data.valid == true or data.valid == "true" then
            print("Key validation successful!")
            return true
        else
            warn("Key validation failed: " .. (data.message or "Invalid key"))
            return false
        end
    end

    local function sendHeartbeat(scriptId, hwid, key, sessionToken)
        if not validateEnvironment() then
            return { continue = false, reason = "Security violation" }
        end

        local heartbeatData = {
            script_id = scriptId,
            hwid = hwid,
            key = key,
            session_token = sessionToken
        }

        local jsonData = game:GetService("HttpService"):JSONEncode(heartbeatData)
        
        local success, response = pcall(function()
            return game:HttpGetAsync("https://projectmadara.com/.netlify/functions/app/script-heartbeat", {
                ["Content-Type"] = "application/json"
            }, jsonData)
        end)

        if not success then
            return { continue = false, reason = "Network error during heartbeat" }
        end

        local data = parseJSON(response)
        return data or { continue = false, reason = "Invalid heartbeat response" }
    end

    local function createKillSwitchProtectedScript(scriptId, userFunction)
        return function()
            -- Get hardware ID
            local hwid = game:GetService("RbxAnalyticsService"):GetClientId()
            
            -- Generate session token
            local sessionToken = tostring(math.random(100000, 999999)) .. "_" .. tostring(tick())
            
            -- Check script status before execution
            print("Checking script authorization...")
            local statusCheck = checkScriptStatus(scriptId, hwid, nil)
            
            if not statusCheck.allowed then
                warn("Script execution blocked: " .. (statusCheck.reason or "Unknown reason"))
                return
            end

            print("Script authorized. Status: " .. (statusCheck.status or "active"))
            
            -- Set up heartbeat system
            local heartbeatInterval = tonumber(statusCheck.heartbeat_interval) or 30
            local isRunning = true
            
            -- Store in global environment to prevent bypassing
            getgenv().scriptRunning = true
            getgenv().killSwitchActive = true
            
            -- Heartbeat coroutine
            local heartbeatCoroutine = coroutine.create(function()
                while isRunning and getgenv().killSwitchActive do
                    wait(heartbeatInterval)
                    
                    if not isRunning or not getgenv().killSwitchActive then
                        break
                    end
                    
                    local heartbeat = sendHeartbeat(scriptId, hwid, nil, sessionToken)
                    
                    if not heartbeat.continue then
                        print("Kill switch activated: " .. (heartbeat.reason or "Script terminated by admin"))
                        isRunning = false
                        getgenv().scriptRunning = false
                        break
                    end
                    
                    -- Update heartbeat interval if changed
                    if heartbeat.next_check then
                        heartbeatInterval = heartbeat.next_check
                    end
                end
            end)
            
            -- Start heartbeat monitoring
            coroutine.resume(heartbeatCoroutine)
            
            -- Wrap user function with kill switch monitoring
            local protectedFunction = function()
                local success, error = pcall(function()
                    while isRunning and getgenv().killSwitchActive and getgenv().scriptRunning do
                        -- Check if main script should continue
                        if userFunction and type(userFunction) == "function" then
                            local functionSuccess, functionError = pcall(userFunction)
                            if not functionSuccess then
                                warn("Script error: " .. tostring(functionError))
                                break
                            end
                        end
                        wait(1) -- Prevent infinite loop
                    end
                end)
                
                if not success then
                    warn("Protected script error: " .. tostring(error))
                end
                
                -- Cleanup
                isRunning = false
                getgenv().scriptRunning = false
                print("Script execution completed")
            end
            
            -- Execute the protected function
            spawn(protectedFunction)
        end
    end

    -- Key System Integration
    local hwid = game:GetService("RbxAnalyticsService"):GetClientId()
    
    -- Create the key validation GUI
    local ScreenGui = Instance.new("ScreenGui")
    local Frame = Instance.new("Frame")
    local Title = Instance.new("TextLabel")
    local KeyBox = Instance.new("TextBox")
    local SubmitButton = Instance.new("TextButton")
    local StatusLabel = Instance.new("TextLabel")
    local CloseButton = Instance.new("TextButton")

    -- GUI Setup
    ScreenGui.Name = "KeySystem"
    ScreenGui.Parent = game.Players.LocalPlayer:WaitForChild("PlayerGui")
    ScreenGui.ResetOnSpawn = false

    Frame.Parent = ScreenGui
    Frame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
    Frame.BorderSizePixel = 0
    Frame.Position = UDim2.new(0.5, -200, 0.5, -150)
    Frame.Size = UDim2.new(0, 400, 0, 300)
    Frame.Active = true
    Frame.Draggable = true

    -- Create corner rounding
    local Corner = Instance.new("UICorner")
    Corner.CornerRadius = UDim.new(0, 10)
    Corner.Parent = Frame

    Title.Parent = Frame
    Title.BackgroundTransparency = 1
    Title.Position = UDim2.new(0, 0, 0, 10)
    Title.Size = UDim2.new(1, 0, 0, 50)
    Title.Font = Enum.Font.GothamBold
    Title.Text = "Script Authorization Required"
    Title.TextColor3 = Color3.fromRGB(255, 255, 255)
    Title.TextSize = 18

    KeyBox.Parent = Frame
    KeyBox.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    KeyBox.BorderSizePixel = 0
    KeyBox.Position = UDim2.new(0.1, 0, 0.3, 0)
    KeyBox.Size = UDim2.new(0.8, 0, 0, 40)
    KeyBox.Font = Enum.Font.Gotham
    KeyBox.PlaceholderText = "Enter your license key..."
    KeyBox.Text = ""
    KeyBox.TextColor3 = Color3.fromRGB(255, 255, 255)
    KeyBox.TextSize = 14

    local KeyCorner = Instance.new("UICorner")
    KeyCorner.CornerRadius = UDim.new(0, 5)
    KeyCorner.Parent = KeyBox

    SubmitButton.Parent = Frame
    SubmitButton.BackgroundColor3 = Color3.fromRGB(0, 162, 255)
    SubmitButton.BorderSizePixel = 0
    SubmitButton.Position = UDim2.new(0.1, 0, 0.55, 0)
    SubmitButton.Size = UDim2.new(0.35, 0, 0, 40)
    SubmitButton.Font = Enum.Font.GothamBold
    SubmitButton.Text = "Verify Key"
    SubmitButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    SubmitButton.TextSize = 14

    local SubmitCorner = Instance.new("UICorner")
    SubmitCorner.CornerRadius = UDim.new(0, 5)
    SubmitCorner.Parent = SubmitButton

    CloseButton.Parent = Frame
    CloseButton.BackgroundColor3 = Color3.fromRGB(255, 60, 60)
    CloseButton.BorderSizePixel = 0
    CloseButton.Position = UDim2.new(0.55, 0, 0.55, 0)
    CloseButton.Size = UDim2.new(0.35, 0, 0, 40)
    CloseButton.Font = Enum.Font.GothamBold
    CloseButton.Text = "Close"
    CloseButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    CloseButton.TextSize = 14

    local CloseCorner = Instance.new("UICorner")
    CloseCorner.CornerRadius = UDim.new(0, 5)
    CloseCorner.Parent = CloseButton

    StatusLabel.Parent = Frame
    StatusLabel.BackgroundTransparency = 1
    StatusLabel.Position = UDim2.new(0.1, 0, 0.75, 0)
    StatusLabel.Size = UDim2.new(0.8, 0, 0, 60)
    StatusLabel.Font = Enum.Font.Gotham
    StatusLabel.Text = "Please enter your license key to continue."
    StatusLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    StatusLabel.TextSize = 12
    StatusLabel.TextWrapped = true
    StatusLabel.TextYAlignment = Enum.TextYAlignment.Top

    -- Button animations and functionality
    local function animateButton(button, hoverColor, normalColor)
        button.MouseEnter:Connect(function()
            button:TweenSize(UDim2.new(button.Size.X.Scale, button.Size.X.Offset, button.Size.Y.Scale, button.Size.Y.Offset + 2), "Out", "Quad", 0.1, true)
            button.BackgroundColor3 = hoverColor
        end)
        
        button.MouseLeave:Connect(function()
            button:TweenSize(UDim2.new(button.Size.X.Scale, button.Size.X.Offset, button.Size.Y.Scale, button.Size.Y.Offset - 2), "Out", "Quad", 0.1, true)
            button.BackgroundColor3 = normalColor
        end)
    end

    animateButton(SubmitButton, Color3.fromRGB(0, 140, 220), Color3.fromRGB(0, 162, 255))
    animateButton(CloseButton, Color3.fromRGB(220, 50, 50), Color3.fromRGB(255, 60, 60))

    -- Key validation logic
    local function validateKey()
        local key = KeyBox.Text:gsub("%s+", "") -- Remove whitespace
        
        if #key == 0 then
            StatusLabel.Text = "Please enter a valid license key."
            StatusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
            return
        end
        
        StatusLabel.Text = "Validating key... Please wait."
        StatusLabel.TextColor3 = Color3.fromRGB(255, 255, 100)
        SubmitButton.Text = "Validating..."
        SubmitButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
        
        -- Check key validity
        if checkKey(key) then
            StatusLabel.Text = "Key validated successfully! Loading script..."
            StatusLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
            
            wait(1)
            ScreenGui:Destroy()
            
            -- Execute the correct function with kill switch protection
            if correctFunction and type(correctFunction) == "function" then
                -- Create a protected version with kill switch
                local scriptId = "main-script-001" -- This should be configurable
                local protectedScript = createKillSwitchProtectedScript(scriptId, correctFunction)
                protectedScript()
            end
        else
            StatusLabel.Text = "Invalid license key. Please check your key and try again."
            StatusLabel.TextColor3 = Color3.fromRGB(255, 100, 100)
            SubmitButton.Text = "Verify Key"
            SubmitButton.BackgroundColor3 = Color3.fromRGB(0, 162, 255)
        end
    end

    -- Button connections
    SubmitButton.MouseButton1Click:Connect(validateKey)
    
    KeyBox.FocusLost:Connect(function(enterPressed)
        if enterPressed then
            validateKey()
        end
    end)
    
    CloseButton.MouseButton1Click:Connect(function()
        ScreenGui:Destroy()
    end)

    -- Anti-tamper protection
    game:GetService("RunService").Heartbeat:Connect(function()
        if not ScreenGui.Parent and not getgenv().scriptRunning then
            -- GUI was destroyed inappropriately
            warn("Key system tampered with!")
            return
        end
    end)
end