   -- User sessions table
   CREATE TABLE user_sessions (
     id SERIAL PRIMARY KEY,
     token TEXT UNIQUE NOT NULL,
     ip_hash TEXT,
     service TEXT,
     current_step INTEGER DEFAULT 1,
     completed_steps INTEGER[],
     status TEXT DEFAULT 'active',
     verification_data JSONB,
     created_at TIMESTAMP DEFAULT NOW(),
     updated_at TIMESTAMP DEFAULT NOW(),
     expires_at TIMESTAMP NOT NULL
   );

   -- Keys table
   CREATE TABLE keys (
     id SERIAL PRIMARY KEY,
     key_code TEXT UNIQUE NOT NULL,
     hwid TEXT,
     ip_hash TEXT,
     session_id TEXT,
     created_by TEXT DEFAULT 'system',
     status TEXT DEFAULT 'active',
     usage_count INTEGER DEFAULT 0,
     metadata JSONB,
     created_at TIMESTAMP DEFAULT NOW(),
     updated_at TIMESTAMP DEFAULT NOW(),
     expires_at TIMESTAMP NOT NULL,
     last_used_at TIMESTAMP
   );

   -- Admin logs table
   CREATE TABLE admin_logs (
     id SERIAL PRIMARY KEY,
     admin_username TEXT NOT NULL,
     action TEXT NOT NULL,
     target_type TEXT,
     target_id TEXT,
     details JSONB,
     ip_address TEXT,
     created_at TIMESTAMP DEFAULT NOW()
   );

   -- Rate limiting table
   CREATE TABLE rate_limits (
     id SERIAL PRIMARY KEY,
     identifier TEXT NOT NULL,
     action TEXT NOT NULL,
     attempts INTEGER DEFAULT 1,
     window_start TIMESTAMP DEFAULT NOW(),
     created_at TIMESTAMP DEFAULT NOW()
   );

   -- Scripts table for kill switch management
   CREATE TABLE scripts (
     id SERIAL PRIMARY KEY,
     script_name TEXT UNIQUE NOT NULL,
     script_id TEXT UNIQUE NOT NULL,
     status TEXT DEFAULT 'active', -- 'active', 'disabled', 'maintenance'
     description TEXT,
     version TEXT DEFAULT '1.0.0',
     script_url TEXT,
     kill_switch_enabled BOOLEAN DEFAULT true,
     maintenance_message TEXT DEFAULT 'Script is currently under maintenance',
     allowed_users TEXT[], -- Array of specific users who can access during maintenance
     usage_count INTEGER DEFAULT 0,
     last_heartbeat TIMESTAMP,
     metadata JSONB DEFAULT '{}',
     created_at TIMESTAMP DEFAULT NOW(),
     updated_at TIMESTAMP DEFAULT NOW()
   );

   -- Script usage tracking table
   CREATE TABLE script_usage (
     id SERIAL PRIMARY KEY,
     script_id TEXT NOT NULL,
     user_hwid TEXT NOT NULL,
     user_key TEXT,
     ip_hash TEXT,
     action TEXT NOT NULL, -- 'start', 'heartbeat', 'stop', 'error', 'blocked'
     status TEXT, -- 'success', 'blocked', 'error'
     error_message TEXT,
     session_duration INTEGER, -- in seconds
     metadata JSONB,
     created_at TIMESTAMP DEFAULT NOW()
   );

   -- Script sessions table for tracking active sessions
   CREATE TABLE script_sessions (
     id SERIAL PRIMARY KEY,
     script_id TEXT NOT NULL,
     user_hwid TEXT NOT NULL,
     user_key TEXT,
     session_token TEXT UNIQUE NOT NULL,
     status TEXT DEFAULT 'active', -- 'active', 'terminated', 'expired'
     ip_hash TEXT,
     started_at TIMESTAMP DEFAULT NOW(),
     last_heartbeat TIMESTAMP DEFAULT NOW(),
     expires_at TIMESTAMP NOT NULL,
     metadata JSONB
   );

   -- Script settings table for dynamic configuration
   CREATE TABLE script_settings (
     id SERIAL PRIMARY KEY,
     script_id TEXT NOT NULL,
     setting_key TEXT NOT NULL,
     setting_value JSONB,
     setting_type TEXT DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
     description TEXT,
     created_at TIMESTAMP DEFAULT NOW(),
     updated_at TIMESTAMP DEFAULT NOW(),
     UNIQUE(script_id, setting_key)
   );

   -- Global system settings for kill switch
   CREATE TABLE system_settings (
     id SERIAL PRIMARY KEY,
     setting_key TEXT UNIQUE NOT NULL,
     setting_value JSONB,
     setting_type TEXT DEFAULT 'string',
     description TEXT,
     created_at TIMESTAMP DEFAULT NOW(),
     updated_at TIMESTAMP DEFAULT NOW()
   );

   -- Insert default system settings
   INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
   ('global_kill_switch', 'false', 'boolean', 'Master kill switch for all scripts'),
   ('maintenance_mode', 'false', 'boolean', 'Global maintenance mode'),
   ('maintenance_message', '"System is under maintenance. Please try again later."', 'string', 'Message shown during maintenance'),
   ('heartbeat_interval', '30', 'number', 'Heartbeat check interval in seconds'),
   ('script_timeout', '300', 'number', 'Script execution timeout in seconds');

   -- Insert sample scripts for testing
   INSERT INTO scripts (script_name, script_id, status, description, version) VALUES
   ('Test Script', 'test-script-001', 'active', 'A test script for kill switch demonstration', '1.0.0'),
   ('Main Script', 'main-script-001', 'active', 'Main production script', '1.0.0'),
   ('Beta Script', 'beta-script-001', 'maintenance', 'Beta testing script', '0.9.0');
