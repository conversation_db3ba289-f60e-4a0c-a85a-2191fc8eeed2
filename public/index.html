<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Project L | Key System</title>
        <link rel="stylesheet" href="styles/main.css" />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <style>
            #debugInfo {
                display: none;
                margin-top: 20px;
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 4px;
                font-family: monospace;
                font-size: 12px;
                white-space: pre-wrap;
                text-align: left;
            }
            .debug-toggle {
                margin-top: 10px;
                font-size: 11px;
                color: #999;
                cursor: pointer;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="content">
                <div class="box">
                    <h1>Project L | Key System</h1>
                    <h2>Choose a Link Shortener</h2>
                    <p>
                        To get your key, please select one of the following link
                        shorteners:
                    </p>
                    <hr />
                    <div class="button-container">
                        <button
                            class="button"
                            onclick="selectService('linkvertise')"
                        >
                            <span>Linkvertise</span>
                        </button>
                        <button
                            class="button"
                            onclick="selectService('shrinkme')"
                        >
                            <span>ShrinkMe</span>
                        </button>
                    </div>
                    <hr />
                    <p>&copy; 2024-2025 Project L. All Rights Reserved.</p>
                    <div class="debug-toggle" onclick="toggleDebug()">
                        Toggle Debug Info
                    </div>
                    <div id="debugInfo"></div>
                </div>
            </div>
        </div>

        <script>
            function selectService(service) {
                // Clear any existing state to start fresh
                localStorage.removeItem("progression_token");
                localStorage.removeItem("step1_completed");
                localStorage.removeItem("step1_pending");
                localStorage.removeItem("step2_completed");
                localStorage.removeItem("step2_pending");
                localStorage.removeItem("lv_token_verified");
                localStorage.removeItem("lv_token_received");
                localStorage.removeItem("link_redirect_time");
                localStorage.removeItem("link_redirect_time_step2");
                localStorage.removeItem("stored_lv_token");

                // Set the selected service
                localStorage.setItem("selected_service", service);

                // Debug info
                updateDebugInfo();

                // Redirect to step 1
                window.location.href = "step1.html";
            }

            function toggleDebug() {
                const debugInfo = document.getElementById("debugInfo");
                if (
                    debugInfo.style.display === "none" ||
                    debugInfo.style.display === ""
                ) {
                    debugInfo.style.display = "block";
                    updateDebugInfo();
                } else {
                    debugInfo.style.display = "none";
                }
            }

            function updateDebugInfo() {
                const debugInfo = document.getElementById("debugInfo");
                if (!debugInfo) return;

                const stateInfo = {
                    progression_token:
                        localStorage.getItem("progression_token"),
                    selected_service: localStorage.getItem("selected_service"),
                    step1_completed: localStorage.getItem("step1_completed"),
                    step1_pending: localStorage.getItem("step1_pending"),
                    step2_completed: localStorage.getItem("step2_completed"),
                    step2_pending: localStorage.getItem("step2_pending"),
                    lv_token_verified:
                        localStorage.getItem("lv_token_verified"),
                    lv_token_received:
                        localStorage.getItem("lv_token_received"),
                    link_redirect_time:
                        localStorage.getItem("link_redirect_time"),
                    link_redirect_time_step2: localStorage.getItem(
                        "link_redirect_time_step2",
                    ),
                };

                debugInfo.textContent = JSON.stringify(stateInfo, null, 2);
            }

            window.onload = function () {
                updateDebugInfo();
            };
        </script>
    </body>
</html>
