<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TheKeySystem - Status</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: #111111;
            border-radius: 16px;
            border: 1px solid #1f1f1f;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-card {
            background: #111111;
            border: 1px solid #1f1f1f;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .status-card h2 {
            color: #ffffff;
            margin-bottom: 16px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-online {
            background: #16a34a;
            box-shadow: 0 0 10px rgba(22, 163, 74, 0.5);
        }

        .status-offline {
            background: #dc2626;
            box-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
        }

        .status-testing {
            background: #f59e0b;
            box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
        }

        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 8px;
            transition: all 0.2s ease;
        }

        .test-button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .test-button:disabled {
            background: #6b7280;
            cursor: not-allowed;
            transform: none;
        }

        .result {
            margin: 16px 0;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .result.success {
            background: rgba(22, 163, 74, 0.1);
            border: 1px solid rgba(22, 163, 74, 0.3);
            color: #16a34a;
        }

        .result.error {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            color: #dc2626;
        }

        .result.info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #3b82f6;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .stat-item {
            background: #1a1a1a;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #3b82f6;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #cccccc;
            margin-top: 4px;
        }

        .nav-links {
            display: flex;
            gap: 16px;
            margin-top: 24px;
            flex-wrap: wrap;
        }

        .nav-link {
            display: inline-block;
            padding: 12px 24px;
            background: #1a1a1a;
            color: #ffffff;
            text-decoration: none;
            border-radius: 8px;
            border: 1px solid #2a2a2a;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background: #222222;
            border-color: #333333;
            transform: translateY(-1px);
        }

        .log-area {
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 8px;
            padding: 16px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 16px;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #666666;
        }

        .log-info { color: #3b82f6; }
        .log-success { color: #16a34a; }
        .log-error { color: #dc2626; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TheKeySystem Status</h1>
            <p>System Health & Testing Dashboard</p>
        </div>

        <!-- System Status -->
        <div class="status-card">
            <h2>
                <span class="status-indicator" id="system-status"></span>
                System Status
            </h2>
            <p id="status-text">Checking system status...</p>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="active-tokens">0</div>
                    <div class="stat-label">Active Tokens</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="active-keys">0</div>
                    <div class="stat-label">Active Keys</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="total-users">0</div>
                    <div class="stat-label">Total Users</div>
                </div>
            </div>
        </div>

        <!-- API Testing -->
        <div class="status-card">
            <h2>API Testing</h2>
            <p>Test the key system endpoints to verify functionality.</p>
            
            <div style="margin: 16px 0;">
                <button class="test-button" onclick="testGenerateToken()">Test Generate Token</button>
                <button class="test-button" onclick="testGenerateKey()">Test Generate Key</button>
                <button class="test-button" onclick="testValidateKey()">Test Validate Key</button>
                <button class="test-button" onclick="testResetHWID()">Test Reset HWID</button>
                <button class="test-button" onclick="testCanGenerate()">Test Can Generate</button>
                <button class="test-button" onclick="clearResults()">Clear Results</button>
            </div>
            
            <div id="test-results"></div>
        </div>

        <!-- System Logs -->
        <div class="status-card">
            <h2>Test Logs</h2>
            <div class="log-area" id="log-area">
                <div class="log-entry log-info">
                    <span class="log-timestamp">[Starting]</span> System status page loaded
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="nav-links">
            <a href="index.html" class="nav-link">← Back to Key System</a>
            <a href="admin/readme.html" class="nav-link">Admin Setup Guide</a>
            <a href="admin/login.html" class="nav-link">Admin Panel</a>
        </div>
    </div>

    <script>
        let testToken = null;
        let testKey = null;

        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            resultsDiv.appendChild(result);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            log('Test results cleared', 'info');
        }

        async function checkSystemStatus() {
            try {
                const response = await fetch('/.netlify/functions/app/debug');
                const data = await response.json();
                
                document.getElementById('system-status').className = 'status-indicator status-online';
                document.getElementById('status-text').textContent = 'System is operational (Demo Mode)';
                
                document.getElementById('active-tokens').textContent = data.tokens_count || 0;
                document.getElementById('active-keys').textContent = data.keys_count || 0;
                document.getElementById('total-users').textContent = data.user_keys_count || 0;
                
                log('System status check successful', 'success');
            } catch (error) {
                document.getElementById('system-status').className = 'status-indicator status-offline';
                document.getElementById('status-text').textContent = 'System unavailable';
                log(`System status check failed: ${error.message}`, 'error');
            }
        }

        async function testGenerateToken() {
            log('Testing token generation...', 'info');
            try {
                const response = await fetch('/.netlify/functions/app/generate-token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        recaptchaResponse: 'test-response-for-demo' 
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    testToken = data.token;
                    showResult(`✅ Token generated successfully: ${data.token.substring(0, 20)}...`, 'success');
                    log('Token generation successful', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                showResult(`❌ Token generation failed: ${error.message}`, 'error');
                log(`Token generation failed: ${error.message}`, 'error');
            }
        }

        async function testGenerateKey() {
            if (!testToken) {
                showResult('❌ Please generate a token first', 'error');
                return;
            }

            log('Testing key generation...', 'info');
            try {
                // First simulate step verification
                await fetch('/.netlify/functions/app/verify-step', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        token: testToken,
                        step: 2,
                        recaptchaResponse: 'test-response'
                    })
                });

                await fetch('/.netlify/functions/app/verify-step', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        token: testToken,
                        step: 3,
                        recaptchaResponse: 'test-response'
                    })
                });

                // Now generate key
                const response = await fetch('/.netlify/functions/app/generate-key', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        token: testToken,
                        skipRecaptcha: true
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    testKey = data.key;
                    showResult(`✅ Key generated: ${data.key}`, 'success');
                    showResult(`📅 Expires: ${new Date(data.expires_at).toLocaleString()}`, 'info');
                    log('Key generation successful', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                showResult(`❌ Key generation failed: ${error.message}`, 'error');
                log(`Key generation failed: ${error.message}`, 'error');
            }
        }

        async function testValidateKey() {
            if (!testKey) {
                showResult('❌ Please generate a key first', 'error');
                return;
            }

            log('Testing key validation...', 'info');
            try {
                const response = await fetch('/.netlify/functions/app/validate-key', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        key: testKey,
                        hwid: 'TEST-HWID-123456789'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ Key validation successful: ${data.message}`, 'success');
                    log('Key validation successful', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                showResult(`❌ Key validation failed: ${error.message}`, 'error');
                log(`Key validation failed: ${error.message}`, 'error');
            }
        }

        async function testResetHWID() {
            if (!testKey) {
                showResult('❌ Please generate a key first', 'error');
                return;
            }

            log('Testing HWID reset...', 'info');
            try {
                const response = await fetch('/.netlify/functions/app/reset-hwid', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        key: testKey
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ HWID reset successful: ${data.message}`, 'success');
                    log('HWID reset successful', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                showResult(`❌ HWID reset failed: ${error.message}`, 'error');
                log(`HWID reset failed: ${error.message}`, 'error');
            }
        }

        async function testCanGenerate() {
            log('Testing can generate check...', 'info');
            try {
                const response = await fetch('/.netlify/functions/app/can-generate-key');
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ Can generate check: ${data.canGenerate ? 'YES' : 'NO'} - ${data.message}`, 'success');
                    log('Can generate check successful', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                showResult(`❌ Can generate check failed: ${error.message}`, 'error');
                log(`Can generate check failed: ${error.message}`, 'error');
            }
        }

        // Initialize page
        window.addEventListener('load', () => {
            checkSystemStatus();
            
            // Auto-refresh system status every 30 seconds
            setInterval(checkSystemStatus, 30000);
        });
    </script>
</body>
</html>