<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            display: inline-block;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success { background-color: #28a745; }
        .btn.danger { background-color: #dc3545; }
        .btn.warning { background-color: #ffc107; color: black; }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-result {
            font-family: monospace;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Admin System Test Suite</h1>
        
        <!-- Login Section -->
        <div class="test-section">
            <h3>1. Admin Authentication Test</h3>
            <input type="text" id="username" placeholder="Username" value="admin1" style="margin-right: 10px; padding: 8px;">
            <input type="password" id="password" placeholder="Password" value="admin123" style="margin-right: 10px; padding: 8px;">
            <button class="btn" onclick="testLogin()">Test Login</button>
            <button class="btn danger" onclick="logout()">Logout</button>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                Tests JWT authentication with the admin API
            </div>
        </div>

        <!-- API Test Section -->
        <div class="test-section">
            <h3>2. Core API Endpoints Test</h3>
            <button class="btn" onclick="testGetScripts()">Get Scripts</button>
            <button class="btn" onclick="testGetSystemSettings()">Get System Settings</button>
            <button class="btn" onclick="testUploadScript()">Test Upload</button>
            <button class="btn" onclick="testScriptServing()">Test Script Serving</button>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                Tests all major backend API endpoints for functionality
            </div>
        </div>

        <!-- Function Test Section -->
        <div class="test-section">
            <h3>3. Backend Endpoint Tests</h3>
            <button class="btn" onclick="testShowUploadModal()">Test Dashboard Access</button>
            <button class="btn" onclick="testRefreshScripts()">Test Scripts Endpoint</button>
            <button class="btn" onclick="testBulkOperations()">Test Bulk Endpoint</button>
            <button class="btn" onclick="testSystemSettings()">Test Settings</button>
            <div style="margin-top: 15px; padding: 10px; background-color: #d1ecf1; border-radius: 5px;">
                <strong>📝 Note:</strong> These tests verify the backend APIs work correctly.
                <br><strong>For JavaScript function testing:</strong>
                <br>• Open <a href="/admin/login.html" target="_blank" style="color: #0c5460;"><strong>Admin Dashboard</strong></a> to test UI functions
                <br>• Or use <a href="/admin-functions-test.html" target="_blank" style="color: #0c5460;"><strong>Advanced Function Tests</strong></a>
            </div>
        </div>

        <!-- Status Display -->
        <div id="status"></div>

        <!-- Test Results -->
        <div id="results"></div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            statusDiv.style.display = 'block';
        }

        function addResult(testName, result, success = true) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result';
            resultDiv.innerHTML = `<strong>${success ? '✅' : '❌'} ${testName}:</strong>\n${result}`;
            resultsDiv.appendChild(resultDiv);
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                showStatus('Testing login...', 'info');
                
                const response = await fetch('/.netlify/functions/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    localStorage.setItem('admin_token', data.token);
                    showStatus(`✅ Login successful! Welcome, ${data.user.username}`, 'success');
                    addResult('Admin Login', `Status: ${response.status}\nUser: ${data.user.username}\nRole: ${data.user.role}\nToken: ${data.token.substring(0, 20)}...`);
                } else {
                    showStatus(`❌ Login failed: ${data.error}`, 'error');
                    addResult('Admin Login', `Status: ${response.status}\nError: ${data.error}`, false);
                }
            } catch (error) {
                showStatus(`❌ Login error: ${error.message}`, 'error');
                addResult('Admin Login', `Error: ${error.message}`, false);
            }
        }

        function logout() {
            localStorage.removeItem('admin_token');
            showStatus('Logged out', 'info');
            addResult('Logout', 'Admin token removed from localStorage');
        }

        async function testGetScripts() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                showStatus('Please login first', 'error');
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/app/admin/scripts', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showStatus(`✅ Found ${data.scripts?.length || 0} scripts`, 'success');
                    addResult('Get Scripts', `Status: ${response.status}\nScripts found: ${data.scripts?.length || 0}\nFirst script: ${data.scripts?.[0]?.script_name || 'None'}`);
                } else {
                    showStatus(`❌ Failed to get scripts: ${data.error}`, 'error');
                    addResult('Get Scripts', `Status: ${response.status}\nError: ${data.error}`, false);
                }
            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
                addResult('Get Scripts', `Error: ${error.message}`, false);
            }
        }

        async function testGetSystemSettings() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                showStatus('Please login first', 'error');
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/app/admin/system', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showStatus(`✅ System settings loaded`, 'success');
                    addResult('System Settings', `Status: ${response.status}\nSettings: ${JSON.stringify(data.settings, null, 2)}`);
                } else {
                    showStatus(`❌ Failed to get settings: ${data.error}`, 'error');
                    addResult('System Settings', `Status: ${response.status}\nError: ${data.error}`, false);
                }
            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
                addResult('System Settings', `Error: ${error.message}`, false);
            }
        }

        async function testUploadScript() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                showStatus('Please login first', 'error');
                return;
            }

            const testScript = {
                script_name: 'Test Script ' + Date.now(),
                script_id: 'test-' + Date.now(),
                script_content: 'print("Hello from test script!")\nprint("Current time:", os.date())',
                description: 'Automated test script',
                version: '1.0.0'
            };

            try {
                const response = await fetch('/.netlify/functions/app/admin/scripts/upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(testScript)
                });

                const data = await response.json();

                if (response.ok) {
                    showStatus(`✅ Script uploaded successfully!`, 'success');
                    addResult('Script Upload', `Status: ${response.status}\nScript ID: ${testScript.script_id}\nURL: ${data.script_url}\nMessage: ${data.message}`);
                } else {
                    showStatus(`❌ Upload failed: ${data.error}`, 'error');
                    addResult('Script Upload', `Status: ${response.status}\nError: ${data.error}`, false);
                }
            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
                addResult('Script Upload', `Error: ${error.message}`, false);
            }
        }

        async function testScriptServing() {
            try {
                const testScriptId = 'test-script-001';
                const scriptUrl = `/.netlify/functions/app/scripts/${testScriptId}.lua`;
                
                const response = await fetch(scriptUrl);
                
                if (response.ok) {
                    const content = await response.text();
                    showStatus(`✅ Script serving works!`, 'success');
                    addResult('Script Serving', `Status: ${response.status}\nScript ID: ${testScriptId}\nContent Length: ${content.length} characters\nFirst 100 chars: ${content.substring(0, 100)}...`);
                } else {
                    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
                    showStatus(`❌ Script serving failed: ${errorData.error}`, 'error');
                    addResult('Script Serving', `Status: ${response.status}\nError: ${errorData.error}`, false);
                }
            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
                addResult('Script Serving', `Error: ${error.message}`, false);
            }
        }

        async function testShowUploadModal() {
            try {
                // Test by opening admin dashboard in iframe or checking if function exists in parent
                const response = await fetch('/admin/index.html');
                if (response.ok) {
                    addResult('Show Upload Modal', 'Admin dashboard HTML exists and accessible');
                    showStatus('✅ Admin dashboard accessible - functions should work in main dashboard', 'success');
                } else {
                    addResult('Show Upload Modal', 'Admin dashboard HTML not accessible', false);
                    showStatus('❌ Admin dashboard not accessible', 'error');
                }
            } catch (error) {
                addResult('Show Upload Modal', `Error: ${error.message}`, false);
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function testRefreshScripts() {
            try {
                // Test by verifying the admin scripts endpoint directly
                const token = localStorage.getItem('admin_token');
                if (!token) {
                    addResult('Refresh Scripts', 'No admin token - login first to test this function', false);
                    showStatus('❌ Need admin token to test script refresh', 'error');
                    return;
                }

                const response = await fetch('/.netlify/functions/app/admin/scripts', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (response.ok) {
                    addResult('Refresh Scripts', 'Scripts endpoint accessible - refresh function should work');
                    showStatus('✅ Scripts endpoint works - refresh function should work in main dashboard', 'success');
                } else {
                    addResult('Refresh Scripts', 'Scripts endpoint not accessible', false);
                    showStatus('❌ Scripts endpoint not accessible', 'error');
                }
            } catch (error) {
                addResult('Refresh Scripts', `Error: ${error.message}`, false);
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function testBulkOperations() {
            try {
                // Test by verifying the bulk endpoint exists
                const token = localStorage.getItem('admin_token');
                if (!token) {
                    addResult('Bulk Operations', 'No admin token - login first to test this function', false);
                    showStatus('❌ Need admin token to test bulk operations', 'error');
                    return;
                }

                // Test with empty operation to see if endpoint exists
                const response = await fetch('/.netlify/functions/app/admin/scripts/bulk', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ operation: 'test', script_ids: [] })
                });
                
                // Even if it fails, if we get a proper response it means the endpoint exists
                if (response.status === 400 || response.status === 200) {
                    addResult('Bulk Operations', 'Bulk operations endpoint exists and accessible');
                    showStatus('✅ Bulk operations endpoint works - function should work in main dashboard', 'success');
                } else {
                    addResult('Bulk Operations', `Bulk endpoint returned status: ${response.status}`, false);
                    showStatus('❌ Bulk operations endpoint issue', 'error');
                }
            } catch (error) {
                addResult('Bulk Operations', `Error: ${error.message}`, false);
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function testSystemSettings() {
            try {
                // Already tested in testGetSystemSettings, so just reference that
                addResult('System Settings Modal', 'System settings functions work - see System Settings API test above');
                showStatus('✅ System settings functions should work in main dashboard', 'success');
            } catch (error) {
                addResult('System Settings Modal', `Error: ${error.message}`, false);
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Auto-run some tests on page load
        window.addEventListener('load', function() {
            const token = localStorage.getItem('admin_token');
            if (token) {
                showStatus('✅ Found existing admin token', 'success');
                addResult('Initial Check', 'Admin token found in localStorage');
            } else {
                showStatus('ℹ️ No admin token found. Please login first.', 'info');
                addResult('Initial Check', 'No admin token found');
            }
        });
    </script>
</body>
</html>