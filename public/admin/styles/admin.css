/* Clean Minimal Admin Dashboard - Mobile Responsive */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --bg-primary: #000000;
    --bg-secondary: #111111;
    --bg-tertiary: #1a1a1a;
    --bg-hover: #222222;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --border: #333333;
    --border-light: #444444;
    --transition: 0.2s ease;
    --sidebar-width: 260px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Layout */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 2000;
    width: 44px;
    height: 44px;
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 8px;
    color: var(--text-primary);
    cursor: pointer;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: var(--transition);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.mobile-menu-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-light);
}

.mobile-menu-btn:active {
    transform: scale(0.95);
}

/* Mobile Overlay */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1500;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.mobile-overlay.active {
    opacity: 1;
    pointer-events: all;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-secondary);
    border-right: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1600;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid var(--border);
    text-align: center;
}

.sidebar-header h2 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.sidebar-header p {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.sidebar-menu {
    list-style: none;
    padding: 16px 12px;
    flex: 1;
    overflow-y: auto;
}

.sidebar-menu li {
    margin-bottom: 4px;
    cursor: pointer;
    transition: var(--transition);
    border-radius: 6px;
}

.sidebar-menu li:hover {
    background: var(--bg-hover);
}

.sidebar-menu li.active {
    background: var(--text-primary);
    color: var(--bg-primary);
}

.sidebar-menu li a,
.sidebar-menu li {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
    min-height: 48px; /* Better touch targets */
}

.sidebar-menu li.active {
    color: var(--bg-primary);
}

.sidebar-menu li i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
    font-size: 0.9rem;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--border);
}

.logout-btn {
    width: 100%;
    padding: 14px;
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 0.9rem;
    min-height: 48px;
}

.logout-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-light);
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    background: var(--bg-primary);
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

.admin-header {
    background: var(--bg-secondary);
    padding: 20px 32px;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left h1 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.header-left p {
    color: var(--text-muted);
    font-size: 0.85rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: 6px;
}

.admin-info span {
    color: var(--text-primary);
    font-size: 0.9rem;
}

.admin-avatar {
    width: 32px;
    height: 32px;
    background: var(--text-primary);
    color: var(--bg-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Content Sections */
.content-section {
    display: none;
    padding: 32px;
}

.content-section.active {
    display: block;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 16px;
    margin-bottom: 32px;
}

.action-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: var(--transition);
    min-height: 80px;
}

.action-card:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-light);
    transform: translateY(-1px);
}

.action-card:active {
    transform: translateY(0);
}

.action-icon {
    width: 48px;
    height: 48px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: 1.1rem;
    flex-shrink: 0;
}

.action-info h4 {
    color: var(--text-primary);
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.action-info p {
    color: var(--text-muted);
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Stats Overview */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 24px;
    transition: var(--transition);
    min-height: 140px;
}

.stat-card:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-light);
    transform: translateY(-1px);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: 1rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
}

.stat-content h2 {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 4px;
    font-weight: 700;
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 4px;
}

.stat-subtitle {
    color: var(--text-muted);
    font-size: 0.75rem;
}

/* Dashboard Main Grid */
.dashboard-main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
}

/* Dashboard Widgets */
.dashboard-widget {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 8px;
    overflow: hidden;
}

.widget-header {
    padding: 20px;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-tertiary);
}

.widget-header h3 {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.widget-actions {
    display: flex;
    gap: 8px;
}

.btn-icon {
    width: 36px;
    height: 36px;
    background: transparent;
    border: 1px solid var(--border);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.btn-icon:hover {
    background: var(--bg-hover);
    border-color: var(--border-light);
    color: var(--text-primary);
}

.btn-icon.danger {
    border-color: #555;
    color: #ff6b6b;
}

.btn-icon.danger:hover {
    background: #2d1515;
    border-color: #ff6b6b;
}

.btn-icon.warning {
    border-color: #555;
    color: #ffd93d;
}

.btn-icon.warning:hover {
    background: #2d2815;
    border-color: #ffd93d;
}

.btn-icon.info {
    border-color: #555;
    color: #74c0fc;
}

.btn-icon.info:hover {
    background: #152d3a;
    border-color: #74c0fc;
}

.widget-content {
    padding: 20px;
}

/* Activity List */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--border);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 36px;
    height: 36px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: 0.9rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    color: var(--text-primary);
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 2px;
}

.activity-details {
    color: var(--text-muted);
    font-size: 0.75rem;
    font-family: 'Monaco', monospace;
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
    margin-bottom: 2px;
    word-break: break-all;
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.7rem;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 16px;
}

.quick-stat {
    text-align: center;
    padding: 16px 8px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.quick-stat:hover {
    background: var(--bg-hover);
    transform: translateY(-1px);
}

.quick-stat-number {
    color: var(--text-primary);
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 4px;
}

.quick-stat-label {
    color: var(--text-muted);
    font-size: 0.75rem;
    font-weight: 500;
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border);
    flex-wrap: wrap;
    gap: 16px;
}

.section-header h2 {
    color: var(--text-primary);
    font-size: 1.4rem;
    font-weight: 600;
}

.section-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border: 1px solid var(--border);
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    background: transparent;
    color: var(--text-secondary);
    min-height: 44px;
    white-space: nowrap;
}

.btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-light);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--text-primary);
    color: var(--bg-primary);
    border-color: var(--text-primary);
}

.btn-primary:hover {
    background: var(--text-secondary);
    border-color: var(--text-secondary);
}

.btn-success {
    background: #2d6a2d;
    color: white;
    border-color: #2d6a2d;
}

.btn-success:hover {
    background: #3d7a3d;
    border-color: #3d7a3d;
}

.btn-danger {
    background: #6a2d2d;
    color: white;
    border-color: #6a2d2d;
}

.btn-danger:hover {
    background: #7a3d3d;
    border-color: #7a3d3d;
}

.btn-warning {
    background: #6a5a2d;
    color: white;
    border-color: #6a5a2d;
}

.btn-warning:hover {
    background: #7a6a3d;
    border-color: #7a6a3d;
}

/* Search Bar */
.search-bar {
    display: flex;
    margin-bottom: 20px;
    gap: 12px;
    flex-wrap: wrap;
}

.search-bar input {
    flex: 1;
    min-width: 200px;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.85rem;
    transition: var(--transition);
}

.search-bar input:focus {
    outline: none;
    border-color: var(--border-light);
    background: var(--bg-tertiary);
}

.search-bar input::placeholder {
    color: var(--text-muted);
}

.search-btn {
    padding: 12px 16px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    min-height: 44px;
}

.search-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-light);
    color: var(--text-primary);
}

/* Tables */
.table-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 8px;
    overflow: hidden;
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

.admin-table th,
.admin-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border);
    font-size: 0.85rem;
    vertical-align: top;
}

.admin-table th {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
}

.admin-table tr:hover {
    background: var(--bg-hover);
}

.key-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.key-value {
    font-family: 'Monaco', monospace;
    background: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.75rem;
    word-break: break-all;
    max-width: 200px;
}

.key-actions {
    display: flex;
    gap: 4px;
}

.action-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: var(--transition);
}

.action-btn.copy {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.action-btn.copy:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.action-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.status-badge.active {
    background: #2d6a2d;
    color: white;
}

.status-badge.expired {
    background: #6a2d2d;
    color: white;
}

.status-badge.revoked {
    background: #dc2626;
    color: white;
    animation: pulse-revoked 2s infinite;
}

.status-badge.pending {
    background: #6a5a2d;
    color: white;
}

.status-badge.warning {
    background: #6a5a2d;
    color: white;
    animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

@keyframes pulse-revoked {
    0% { opacity: 1; }
    50% { opacity: 0.8; }
    100% { opacity: 1; }
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.setting-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 20px;
}

.setting-card h3 {
    color: var(--text-primary);
    margin-bottom: 16px;
    font-size: 1rem;
    font-weight: 600;
}

.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-size: 0.85rem;
    font-weight: 500;
}

.setting-item input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.setting-item input[type="number"] {
    width: 100%;
    padding: 12px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.85rem;
}

.setting-item input[type="number"]:focus {
    outline: none;
    border-color: var(--border-light);
}

/* Logs */
.logs-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 16px;
    height: 400px;
    overflow-y: auto;
    font-family: 'Monaco', monospace;
    font-size: 0.8rem;
}

.log-entry {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    word-break: break-word;
}

.log-entry.info {
    background: rgba(116, 192, 252, 0.1);
    color: #74c0fc;
}

.log-entry.warning {
    background: rgba(255, 217, 61, 0.1);
    color: #ffd93d;
}

.log-entry.error {
    background: rgba(255, 107, 107, 0.1);
    color: #ff6b6b;
}

/* Charts */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 20px;
}

.chart-card h3 {
    color: var(--text-primary);
    margin-bottom: 16px;
    font-size: 1rem;
    font-weight: 600;
}

.css-chart {
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: 6px;
    padding: 16px;
    min-height: 250px;
    height: 250px;
    position: relative;
    overflow: hidden;
}

/* Notifications */
.admin-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 350px;
    z-index: 10000;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.admin-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid var(--border);
    font-size: 0.85rem;
    font-weight: 500;
    word-break: break-word;
}

.notification-success .notification-content {
    background: #2d6a2d;
    color: white;
}

.notification-error .notification-content {
    background: #6a2d2d;
    color: white;
}

.notification-warning .notification-content {
    background: #6a5a2d;
    color: white;
}

.notification-info .notification-content {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    margin-left: auto;
    opacity: 0.7;
    min-width: 24px;
    height: 24px;
}

.notification-close:hover {
    opacity: 1;
}

/* Loading */
.loading {
    text-align: center;
    color: var(--text-muted);
    padding: 40px 20px;
    font-size: 0.9rem;
}

.error {
    text-align: center;
    color: #ff6b6b;
    padding: 40px 20px;
    font-size: 0.9rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Mobile Responsive Breakpoints */
@media (max-width: 1024px) {
    .dashboard-main {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stats-overview {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }
}

@media (max-width: 768px) {
    :root {
        --sidebar-width: 280px;
    }
    
    /* Show mobile menu button */
    .mobile-menu-btn {
        display: flex;
    }
    
    /* Show mobile overlay */
    .mobile-overlay {
        display: block;
    }
    
    /* Hide sidebar by default on mobile */
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    /* Adjust main content */
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    /* Header adjustments */
    .admin-header {
        padding: 16px 20px 16px 80px;
    }
    
    .header-left h1 {
        font-size: 1.3rem;
    }
    
    .admin-info span {
        display: none;
    }
    
    /* Content padding */
    .content-section {
        padding: 20px 16px;
    }
    
    /* Quick actions mobile layout */
    .quick-actions {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .action-card {
        padding: 16px;
        min-height: auto;
    }
    
    .action-icon {
        width: 40px;
        height: 40px;
    }
    
    /* Stats grid mobile */
    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .stat-card {
        padding: 16px;
        min-height: 120px;
    }
    
    /* Dashboard main mobile */
    .dashboard-main {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    /* Widget adjustments */
    .widget-content {
        padding: 16px;
    }
    
    .activity-item {
        padding: 10px 0;
    }
    
    /* Section headers mobile */
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .section-actions {
        width: 100%;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    /* Search bar mobile */
    .search-bar {
        flex-direction: column;
        gap: 8px;
    }
    
    .search-bar input {
        min-width: auto;
    }
    
    /* Table mobile */
    .table-container {
        margin: 0 -16px;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
    
    .admin-table {
        min-width: 700px;
        font-size: 0.8rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 10px 8px;
    }
    
    .key-value {
        max-width: 120px;
        font-size: 0.7rem;
    }
    
    /* Settings mobile */
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    /* Analytics mobile */
    .analytics-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .chart-card {
        padding: 16px;
    }
    
    /* Logs mobile */
    .logs-container {
        height: 300px;
        margin: 0 -16px;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
}

@media (max-width: 600px) {
    /* Ultra mobile adjustments */
    .admin-header {
        padding: 12px 16px 12px 70px;
    }
    
    .header-left h1 {
        font-size: 1.1rem;
    }
    
    .header-left p {
        font-size: 0.75rem;
    }
    
    .content-section {
        padding: 16px 12px;
    }
    
    /* Single column stats */
    .stats-overview {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .stat-card {
        padding: 12px;
        min-height: 100px;
    }
    
    .stat-content h2 {
        font-size: 1.6rem;
    }
    
    /* Action cards smaller */
    .action-card {
        padding: 12px;
        gap: 12px;
    }
    
    .action-icon {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }
    
    .action-info h4 {
        font-size: 0.9rem;
    }
    
    .action-info p {
        font-size: 0.75rem;
    }
    
    /* Smaller buttons */
    .btn {
        padding: 10px 14px;
        font-size: 0.8rem;
        min-height: 40px;
    }
    
    .btn-icon {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
    
    /* Mobile menu button adjustments */
    .mobile-menu-btn {
        width: 40px;
        height: 40px;
        top: 16px;
        left: 16px;
        font-size: 1.1rem;
    }
    
    /* Quick stats mobile */
    .quick-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .quick-stat {
        padding: 10px 6px;
    }
    
    .quick-stat-number {
        font-size: 1.1rem;
    }
    
    .quick-stat-label {
        font-size: 0.65rem;
    }
    
    /* Activity items mobile */
    .activity-icon {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
    
    .activity-title {
        font-size: 0.8rem;
    }
    
    .activity-details {
        font-size: 0.7rem;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .activity-time {
        font-size: 0.65rem;
    }
    
    /* Table mobile optimizations */
    .admin-table {
        min-width: 600px;
        font-size: 0.75rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 8px 6px;
    }
    
    .key-value {
        max-width: 100px;
        font-size: 0.65rem;
        padding: 2px 4px;
    }
    
    .action-btn {
        width: 24px;
        height: 24px;
        font-size: 0.7rem;
    }
    
    .status-badge {
        padding: 2px 6px;
        font-size: 0.6rem;
    }
    
    /* Notification mobile */
    .admin-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .notification-content {
        padding: 12px;
        font-size: 0.8rem;
    }
}

@media (max-width: 400px) {
    /* Very small screens */
    .mobile-menu-btn {
        width: 36px;
        height: 36px;
        top: 12px;
        left: 12px;
        font-size: 1rem;
    }
    
    .admin-header {
        padding: 10px 12px 10px 60px;
    }
    
    .content-section {
        padding: 12px 8px;
    }
    
    .action-card {
        padding: 10px;
        gap: 10px;
    }
    
    .action-icon {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
    
    .stat-card {
        padding: 10px;
    }
    
    .stat-content h2 {
        font-size: 1.4rem;
    }
    
    .quick-stats {
        grid-template-columns: 1fr;
        gap: 6px;
    }
}

/* Chart responsive styles */
.chart-bars {
    display: flex;
    align-items: flex-end;
    height: 150px;
    gap: 8px;
    margin-bottom: 16px;
    padding: 0 8px;
}

.chart-bar {
    background: linear-gradient(to top, #3b82f6, #2563eb);
    border-radius: 3px;
    min-width: 20px;
    max-width: 60px;
    flex: 1;
    position: relative;
    transition: all 0.3s ease;
    min-height: 10px;
}

.chart-bar:hover {
    background: linear-gradient(to top, #2563eb, #1d4ed8);
    transform: scaleY(1.05);
}

.chart-bar-value {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--text-secondary);
    font-size: 0.7rem;
    font-weight: 600;
}

.chart-line {
    position: relative;
    height: 150px;
    margin-bottom: 16px;
    padding: 0 8px;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        transparent 25%,
        var(--border) 25%,
        var(--border) 25.5%,
        transparent 25.6%,
        transparent 50%,
        var(--border) 50%,
        var(--border) 50.5%,
        transparent 50.6%,
        transparent 75%,
        var(--border) 75%,
        var(--border) 75.5%,
        transparent 75.6%,
        transparent 100%
    );
}

.chart-point {
    width: 10px;
    height: 10px;
    background: #16a34a;
    border-radius: 50%;
    position: absolute;
    box-shadow: 0 0 8px rgba(22, 163, 74, 0.5);
    border: 2px solid #0f172a;
    margin-left: -5px;
    transition: all 0.3s ease;
}

.chart-point:hover {
    transform: scale(1.2);
    box-shadow: 0 0 12px rgba(22, 163, 74, 0.8);
}

.chart-line-path {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    color: var(--text-muted);
    font-size: 0.7rem;
    margin-top: 8px;
    padding: 0 8px;
    flex-wrap: wrap;
    gap: 4px;
}

.chart-labels span {
    flex: 1;
    text-align: center;
    min-width: 60px;
}

.chart-summary {
    margin-top: 16px;
    padding: 12px;
    background: var(--bg-primary);
    border-radius: 6px;
    border: 1px solid var(--border);
}

.chart-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.chart-summary-item:last-child {
    margin-bottom: 0;
}

.chart-summary-value {
    color: var(--text-primary);
    font-weight: 600;
}

.chart-no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 150px;
    color: var(--text-muted);
    font-style: italic;
}

@media (max-width: 768px) {
    .chart-bars {
        height: 120px;
        gap: 4px;
    }
    
    .chart-bar {
        min-width: 15px;
    }
    
    .chart-line {
        height: 120px;
    }
    
    .chart-labels {
        font-size: 0.65rem;
    }
    
    .chart-labels span {
        min-width: 40px;
    }
}