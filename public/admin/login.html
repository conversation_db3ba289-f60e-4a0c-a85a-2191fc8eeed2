<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin <PERSON> - TheKeySystem</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --error-color: #dc2626;
            --success-color: #16a34a;
            --background-color: #0f172a;
            --card-background: #1e293b;
            --text-color: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            position: relative;
            z-index: 10;
        }

        .login-card {
            background: var(--card-background);
            border: 1px solid #334155;
            border-radius: 24px;
            padding: 48px;
            width: 420px;
            backdrop-filter: blur(20px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            animation: slideIn 0.6s ease-out;
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, #64748b 50%, transparent 100%);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            width: 72px;
            height: 72px;
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 1.8rem;
            color: white;
        }

        .login-header h1 {
            color: #ffffff;
            font-size: 1.8rem;
            margin-bottom: 8px;
            font-weight: 700;
            letter-spacing: -0.02em;
        }

        .login-header p {
            color: #94a3b8;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .login-form {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #e2e8f0;
            margin-bottom: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group label i {
            color: var(--primary-color);
            width: 16px;
        }

        .form-group input {
            width: 100%;
            padding: 16px 20px;
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 14px;
            color: white;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: #1e293b;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-group input::placeholder {
            color: #64748b;
        }

        .login-btn {
            width: 100%;
            padding: 16px;
            background: var(--primary-color);
            color: #ffffff;
            border: none;
            border-radius: 14px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            letter-spacing: -0.01em;
        }

        .login-btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #334155;
        }

        .login-footer p {
            color: #64748b;
            font-size: 0.8rem;
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }

        /* Error/Success Messages */
        .message {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            animation: fadeIn 0.3s ease;
        }

        .message.error {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            color: var(--error-color);
        }

        .message.success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: var(--success-color);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Loading State */
        .login-btn.loading {
            position: relative;
            color: transparent;
        }

        .login-btn.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-card {
                width: 90%;
                padding: 30px 20px;
                margin: 20px;
            }
            
            .logo {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
            
            .login-header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1>Admin Access</h1>
                <p>Enter your admin credentials to continue</p>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        Username
                    </label>
                    <input type="text" id="username" name="username" placeholder="Enter your username" required autocomplete="username">
                </div>
                
                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <input type="password" id="password" name="password" placeholder="Enter your password" required autocomplete="current-password">
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In
                </button>
            </form>

            <div class="login-footer">
                <p>&copy; 2024 TheKeySystem Admin Panel</p>
                <p><a href="../index.html">← Back to Key System</a> | <a href="readme.html">Setup Guide</a></p>
            </div>
        </div>
    </div>

    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    
    <script>
        // Admin Login JavaScript
        class AdminLogin {
            constructor() {
                this.apiBase = '/.netlify/functions/admin';
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.checkExistingAuth();
            }

            setupEventListeners() {
                const loginForm = document.getElementById('loginForm');
                loginForm.addEventListener('submit', (e) => this.handleLogin(e));

                // Enter key support for inputs
                document.getElementById('username').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        document.getElementById('password').focus();
                    }
                });

                document.getElementById('password').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleLogin(e);
                    }
                });
            }

            async handleLogin(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();
                const loginBtn = document.getElementById('loginBtn');
                
                if (!username || !password) {
                    this.showMessage('Please enter both username and password', 'error');
                    return;
                }

                // Show loading state
                loginBtn.classList.add('loading');
                loginBtn.disabled = true;

                try {
                    const response = await fetch(`${this.apiBase}/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ username, password })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        
                        // Store the token and user info
                        localStorage.setItem('admin_token', data.token);
                        localStorage.setItem('admin_user', JSON.stringify(data.user));
                        localStorage.setItem('admin_auth_time', Date.now().toString());
                        
                        this.showMessage('Login successful! Redirecting...', 'success');
                        
                        // Redirect to dashboard
                        setTimeout(() => {
                            window.location.href = '/admin/index.html';
                        }, 1000);
                    } else if (response.status === 401) {
                        this.showMessage('Invalid username or password. Please try again.', 'error');
                    } else {
                        const errorData = await response.json();
                        this.showMessage(errorData.error || 'Login failed. Please try again.', 'error');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    this.showMessage('Login failed. Server may be unavailable. Please try again later.', 'error');
                } finally {
                    // Remove loading state
                    loginBtn.classList.remove('loading');
                    loginBtn.disabled = false;
                }
            }

            showMessage(text, type) {
                // Remove existing messages
                const existingMessage = document.querySelector('.message');
                if (existingMessage) {
                    existingMessage.remove();
                }

                // Create new message
                const message = document.createElement('div');
                message.className = `message ${type}`;
                
                const icon = type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle';
                message.innerHTML = `
                    <i class="${icon}"></i>
                    <span>${text}</span>
                `;

                // Insert before form
                const form = document.getElementById('loginForm');
                form.parentNode.insertBefore(message, form);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (message.parentNode) {
                        message.remove();
                    }
                }, 5000);
            }

            checkExistingAuth() {
                const token = localStorage.getItem('admin_token');
                const authTime = localStorage.getItem('admin_auth_time');
                
                if (token && authTime) {
                    const now = Date.now();
                    const authAge = now - parseInt(authTime);
                    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
                    
                    if (authAge < maxAge) {
                        // Check if token is still valid by making a request to /me endpoint
                        this.verifyToken(token);
                    } else {
                        // Expired, clear storage
                        localStorage.removeItem('admin_token');
                        localStorage.removeItem('admin_user');
                        localStorage.removeItem('admin_auth_time');
                    }
                }
            }

            async verifyToken(token) {
                try {
                    const response = await fetch(`${this.apiBase}/me`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        // Token is still valid, redirect to dashboard
                        window.location.href = '/admin/index.html';
                    } else {
                        // Token is invalid, clear storage
                        localStorage.removeItem('admin_token');
                        localStorage.removeItem('admin_user');
                        localStorage.removeItem('admin_auth_time');
                    }
                } catch (error) {
                    // Network error, don't redirect but also don't clear storage
                    console.warn('Token verification failed due to network error:', error);
                }
            }
        }

        // Initialize login system
        document.addEventListener('DOMContentLoaded', () => {
            new AdminLogin();
            
            // Focus on username field
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>