<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Madara - Admin Dashboard</title>
    <link rel="stylesheet" href="styles/admin.css">
    <style>
        /* Font Awesome Icons - Inline CSS to avoid CSP issues */
        .fas, .fa {
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
        }

        /* Icon definitions */
        .fa-shield-alt:before { content: "🛡"; }
        .fa-tachometer-alt:before { content: "📊"; }
        .fa-key:before { content: "🔑"; }
        .fa-users:before { content: "👥"; }
        .fa-chart-bar:before { content: "📈"; }
        .fa-file-alt:before { content: "📄"; }
        .fa-cog:before { content: "⚙"; }
        .fa-sign-out-alt:before { content: "🚪"; }
        .fa-plus:before { content: "+"; }
        .fa-broom:before { content: "🧹"; }
        .fa-arrow-up:before { content: "↑"; }
        .fa-arrow-down:before { content: "↓"; }
        .fa-sync:before { content: "↻"; }
        .fa-trash:before { content: "🗑"; }
        .fa-clock:before { content: "🕐"; }
        .fa-ban:before { content: "🚫"; }
        .fa-search:before { content: "🔍"; }
        .fa-save:before { content: "💾"; }
        .fa-user-shield:before { content: "👤"; }
        .fa-copy:before { content: "📋"; }
        .fa-check-circle:before { content: "✅"; }
        .fa-exclamation-circle:before { content: "⚠"; }
        .fa-exclamation-triangle:before { content: "⚠"; }
        .fa-info-circle:before { content: "ℹ"; }
        .fa-times:before { content: "✕"; }
        .fa-power-off:before { content: "⚡"; }
        .fa-code:before { content: "💻"; }
        .fa-tools:before { content: "🔧"; }
        .fa-play:before { content: "▶"; }
        .fa-pause:before { content: "⏸"; }
        .fa-stop:before { content: "⏹"; }
        .fa-edit:before { content: "✏"; }
    </style>
</head>
<body>
    <!-- Mobile Menu Button -->
    <button class="mobile-menu-btn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay"></div>

    <div class="admin-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> Admin Panel</h2>
                <p>TheKeySystem Dashboard</p>
            </div>

            <ul class="sidebar-menu">
                <li class="active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </li>
                <li data-section="keys">
                    <i class="fas fa-key"></i>
                    <span>Key Management</span>
                </li>
                <li data-section="users">
                    <i class="fas fa-users"></i>
                    <span>User Management</span>
                </li>
                <li data-section="analytics">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </li>
                <li data-section="scripts">
                    <i class="fas fa-code"></i>
                    <span>Script Manager</span>
                </li>
                <li data-section="logs">
                    <i class="fas fa-file-alt"></i>
                    <span>System Logs</span>
                </li>
                <li data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </li>
            </ul>

            <div class="sidebar-footer">
                <button class="logout-btn" data-action="logout">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="admin-header">
                <div class="header-left">
                    <h1 id="page-title">Dashboard</h1>
                    <p id="page-subtitle">System Overview</p>
                </div>
                <div class="header-right">
                    <div class="admin-info">
                        <span>Welcome, Admin</span>
                        <div class="admin-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                    </div>
                    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Theme" style="position: relative; top: 0; right: 0; margin-left: 15px; flex-shrink: 0;">
                        <span id="themeIcon">🌙</span> <span id="themeText">Dark Mode</span>
                    </button>
                </div>
            </header>

            <!-- Modern Dashboard Section -->
            <section id="dashboard-section" class="content-section active">
                <!-- Quick Actions Bar -->
                <div class="quick-actions">
                    <div class="action-card" data-action="generateNewKeys">
                        <div class="action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="action-info">
                            <h4>Generate Keys</h4>
                            <p>Create new license keys</p>
                        </div>
                    </div>

                    <div class="action-card" data-action="cleanupExpiredKeys">
                        <div class="action-icon">
                            <i class="fas fa-broom"></i>
                        </div>
                        <div class="action-info">
                            <h4>System Cleanup</h4>
                            <p>Remove expired entries</p>
                        </div>
                    </div>

                    <div class="action-card" data-action="switchToKeys">
                        <div class="action-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="action-info">
                            <h4>Key Management</h4>
                            <p>Monitor & control keys</p>
                        </div>
                    </div>

                    <div class="action-card" data-action="switchToUsers">
                        <div class="action-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="action-info">
                            <h4>User Analytics</h4>
                            <p>Track user activity</p>
                        </div>
                    </div>

                    <div class="action-card" data-action="showExpiredKeys" style="border: 1px solid rgba(255, 107, 107, 0.3);">
                        <div class="action-icon" style="background: rgba(255, 107, 107, 0.1); color: #ff6b6b;">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="action-info">
                            <h4 style="color: #ff6b6b;">Expired Keys</h4>
                            <p>View & manage expired keys</p>
                        </div>
                    </div>
                </div>

                <!-- Stats Overview -->
                <div class="stats-overview">
                    <div class="stat-card primary">
                        <div class="stat-header">
                            <div class="stat-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+12%</span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h2 id="total-keys">Loading...</h2>
                            <p>Total Keys Generated</p>
                            <div class="stat-subtitle">All time statistics</div>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-header">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+8%</span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h2 id="active-keys-stat">Loading...</h2>
                            <p>Active Keys</p>
                            <div class="stat-subtitle">Currently valid</div>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-header">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-down"></i>
                                <span>-3%</span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h2 id="today-keys-stat">0</h2>
                            <p>Today's Keys</p>
                            <div class="stat-subtitle">Generated today</div>
                        </div>
                    </div>
                </div>

                <!-- Main Dashboard Grid -->
                <div class="dashboard-main">
                    <!-- Recent Activity -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>Recent Activity</h3>
                            <div class="widget-actions">
                                <button class="btn-icon" data-action="refreshDashboard">
                                    <i class="fas fa-sync"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="activity-list" id="recent-keys">
                                <div class="loading">Loading recent activity...</div>
                            </div>
                        </div>
                    </div>

                    <!-- System Health -->
                    <div class="dashboard-widget">
                        <div class="widget-header">
                            <h3>System Health</h3>
                            <div class="widget-actions">
                                <button class="btn-icon" data-action="refreshHealth">
                                    <i class="fas fa-sync"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="quick-stats">
                                <div class="quick-stat">
                                    <div class="quick-stat-number" id="today-keys">0</div>
                                    <div class="quick-stat-label">Today</div>
                                </div>
                                <div class="quick-stat">
                                    <div class="quick-stat-number" id="today-users">0</div>
                                    <div class="quick-stat-label">Users</div>
                                </div>
                                <div class="quick-stat" style="cursor: pointer;" data-action="showExpiredKeys" title="Click to view expired keys">
                                    <div class="quick-stat-number" id="expired-keys">0</div>
                                    <div class="quick-stat-label">Expired</div>
                                </div>
                                <div class="quick-stat">
                                    <div class="quick-stat-number">99.9%</div>
                                    <div class="quick-stat-label">Uptime</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Key Management Section -->
            <section id="keys-section" class="content-section">
                <div class="section-header">
                    <h2>Key Management</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" data-action="loadKeys">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-success" data-action="generateNewKeys">
                            <i class="fas fa-plus"></i> Generate Keys
                        </button>
                        <button class="btn btn-danger" data-action="cleanupExpiredKeys">
                            <i class="fas fa-trash"></i> Clean Expired
                        </button>
                    </div>
                </div>

                <div class="search-bar">
                    <input type="text" id="key-search" placeholder="Search keys...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-warning" data-action="showExpiredKeys" style="margin-left: 12px;">
                        <i class="fas fa-exclamation-triangle"></i> Show Expired
                    </button>
                </div>

                <div class="table-container">
                    <table class="admin-table" id="keys-table">
                        <thead>
                            <tr>
                                <th>Key</th>
                                <th>HWID</th>
                                <th>Created</th>
                                <th>Expires</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="keys-tbody">
                            <tr>
                                <td colspan="6" class="loading">Loading keys...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- User Management Section -->
            <section id="users-section" class="content-section">
                <div class="section-header">
                    <h2>User Management</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" data-action="loadUsers">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                </div>

                <div class="search-bar">
                    <input type="text" id="user-search" placeholder="Search by HWID or IP...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <div class="table-container">
                    <table class="admin-table" id="users-table">
                        <thead>
                            <tr>
                                <th>HWID</th>
                                <th>IP Hash</th>
                                <th>Keys Generated</th>
                                <th>Last Used</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="users-tbody">
                            <tr>
                                <td colspan="6" class="loading">Loading users...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics-section" class="content-section">
                <div class="section-header">
                    <h2>Analytics</h2>
                    <div class="section-actions">
                        <select id="analytics-period">
                            <option value="24h">Last 24 Hours</option>
                            <option value="7d">Last 7 Days</option>
                            <option value="30d">Last 30 Days</option>
                        </select>
                    </div>
                </div>

                <div class="analytics-grid">
                    <div class="chart-card">
                        <h3>Key Generation Over Time</h3>
                        <div id="keys-chart" class="css-chart"></div>
                    </div>

                    <div class="chart-card">
                        <h3>User Activity</h3>
                        <div id="activity-chart" class="css-chart"></div>
                    </div>
                </div>
            </section>

            <!-- Script Manager Section -->
            <section id="scripts-section" class="content-section">
                <!-- Top Action Buttons -->
                <div style="display: flex; gap: 15px; margin-bottom: 25px; padding: 20px; background: var(--bg-secondary); border-radius: 12px; border: 1px solid var(--border-color); justify-content: flex-start; flex-wrap: wrap; box-shadow: 0 4px 15px var(--shadow-light);">
                    <button class="btn btn-success prominent-upload-btn" onclick="showUploadScriptModal()" style="font-size: 16px; padding: 15px 25px; font-weight: 600; border-radius: 10px; box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3); transition: all 0.3s ease;">
                        <i class="fas fa-upload"></i> Upload New Script
                    </button>
                    <button class="btn btn-info prominent-refresh-btn" onclick="refreshScripts()" style="font-size: 16px; padding: 15px 25px; font-weight: 600; border-radius: 10px; box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3); transition: all 0.3s ease;">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>

                <div class="section-header">
                    <h2>⚡ Script Manager</h2>
                </div>

                <!-- Modern Stats Dashboard -->
                <div class="modern-stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-bottom: 25px;">
                    <div class="modern-stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <div class="stat-icon">🚀</div>
                        <div class="stat-content">
                            <h3 id="activeScriptsCount">-</h3>
                            <p>Active Scripts</p>
                        </div>
                        <div class="stat-toggle">
                            <label class="modern-switch">
                                <input type="checkbox" id="globalKillSwitch" onchange="toggleGlobalKillSwitch()">
                                <span class="switch-slider"></span>
                            </label>
                        </div>
                    </div>

                    <div class="modern-stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        <div class="stat-icon">🔧</div>
                        <div class="stat-content">
                            <h3 id="maintenanceScriptsCount">-</h3>
                            <p>Maintenance Mode</p>
                        </div>
                        <div class="stat-toggle">
                            <label class="modern-switch">
                                <input type="checkbox" id="maintenanceMode" onchange="toggleMaintenanceMode()">
                                <span class="switch-slider"></span>
                            </label>
                        </div>
                    </div>

                    <div class="modern-stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <h3 id="totalScriptUsage">-</h3>
                            <p>Total Executions</p>
                        </div>
                    </div>

                    <div class="modern-stat-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-content">
                            <h3 id="totalScriptsCount">-</h3>
                            <p>Total Scripts</p>
                        </div>
                    </div>
                </div>

                <!-- Modern Action Bar -->
                <div class="modern-action-bar" style="background: var(--bg-primary); border-radius: 15px; padding: 20px; box-shadow: 0 8px 25px var(--shadow-light); margin-bottom: 25px; border: 1px solid var(--border-color);">
                    <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 15px;">
                        <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                            <button class="modern-btn modern-btn-success" onclick="enableAllScripts()">
                                <i class="fas fa-play"></i> Enable All
                            </button>
                            <button class="modern-btn modern-btn-warning" onclick="maintenanceAllScripts()">
                                <i class="fas fa-tools"></i> Maintenance
                            </button>
                            <button class="modern-btn modern-btn-danger" onclick="disableAllScripts()">
                                <i class="fas fa-stop"></i> Disable All
                            </button>
                        </div>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <button class="modern-btn modern-btn-secondary" onclick="showBulkModal()">
                                <i class="fas fa-tasks"></i> Bulk Actions
                            </button>
                            <button class="modern-btn modern-btn-secondary" onclick="exportScripts()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter Bar -->
                <div class="search-filter-bar" style="background: var(--bg-primary); border-radius: 15px; padding: 20px; box-shadow: 0 8px 25px var(--shadow-light); margin-bottom: 25px; border: 1px solid var(--border-color);">
                    <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                        <div style="flex: 1; min-width: 250px;">
                            <div style="position: relative;">
                                <input type="text" id="scriptSearchInput" placeholder="Search scripts by name or ID..."
                                       style="width: 100%; padding: 12px 45px 12px 15px; border: 2px solid var(--border-color); border-radius: 10px; font-size: 14px; transition: all 0.3s ease; background: var(--bg-secondary); color: var(--text-primary);"
                                       onkeyup="if(event.key === 'Enter') filterScripts()">
                                <i class="fas fa-search" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: #6c757d;"></i>
                            </div>
                        </div>
                        <select id="statusFilter" style="padding: 12px 15px; border: 2px solid var(--border-color); border-radius: 10px; background: var(--bg-secondary); font-size: 14px; min-width: 130px; color: var(--text-primary);">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="disabled">Disabled</option>
                            <option value="maintenance">Maintenance</option>
                        </select>
                        <button class="modern-btn modern-btn-primary" onclick="filterScripts()">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <button class="modern-btn modern-btn-secondary" onclick="clearScriptFilters()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>

                <!-- Modern Scripts Grid -->
                <div class="scripts-grid-container">
                    <div id="scriptsGrid" class="scripts-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 20px;">
                        <!-- Script cards will be populated here -->
                    </div>

                    <!-- Empty State -->
                    <div id="emptyScriptsState" class="empty-state" style="display: none; text-align: center; padding: 60px 20px; background: var(--bg-primary); border-radius: 15px; border: 2px dashed var(--border-color);">
                        <div style="font-size: 48px; margin-bottom: 20px;">📝</div>
                        <h3 style="color: #6c757d; margin-bottom: 10px;">No Scripts Found</h3>
                        <p style="color: #adb5bd; margin-bottom: 25px;">Upload your first script to get started with your script management system.</p>
                        <button class="modern-btn modern-btn-primary" onclick="showUploadScriptModal()">
                            <i class="fas fa-upload"></i> Upload Your First Script
                        </button>
                    </div>
                </div>
            </section>

            <!-- Logs Section -->
            <section id="logs-section" class="content-section">
                <div class="section-header">
                    <h2>System Logs</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" data-action="loadLogs">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-secondary" data-action="clearLogs">
                            <i class="fas fa-trash"></i> Clear Logs
                        </button>
                    </div>
                </div>

                <div class="logs-container" id="logs-container">
                    <div class="loading">Loading logs...</div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="content-section">
                <div class="section-header">
                    <h2>System Settings</h2>
                </div>

                <div class="settings-grid">
                    <div class="setting-card">
                        <h3>Key Generation</h3>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="enable-keygen" checked>
                                Enable Key Generation
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>Default Key Duration (hours):</label>
                            <input type="number" id="key-duration" value="24" min="1" max="168">
                        </div>
                        <div class="setting-item">
                            <label>Daily Key Limit per User:</label>
                            <input type="number" id="daily-limit" value="1" min="1" max="10">
                        </div>
                    </div>

                    <div class="setting-card">
                        <h3>Security</h3>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="enable-hwid" checked>
                                Enable HWID Binding
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="enable-ratelimit" checked>
                                Enable Rate Limiting
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="enable-captcha" checked>
                                Require reCAPTCHA
                            </label>
                        </div>
                    </div>
                </div>

                <button class="btn btn-success" data-action="saveSettings">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </section>
        </main>
    </div>

    <!-- Load admin dashboard script -->
    <script src="scripts/admin.js"></script>

    <!-- Add Script Modal -->
    <div id="addScriptModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Script</h3>
                <span class="close" onclick="closeModal('addScriptModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addScriptForm">
                    <div class="form-group">
                        <label for="addScriptName">Script Name:</label>
                        <input type="text" id="addScriptName" required>
                    </div>
                    <div class="form-group">
                        <label for="addScriptId">Script ID:</label>
                        <input type="text" id="addScriptId" placeholder="e.g., my-script-001" required>
                    </div>
                    <div class="form-group">
                        <label for="addScriptDescription">Description:</label>
                        <textarea id="addScriptDescription" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="addScriptVersion">Version:</label>
                        <input type="text" id="addScriptVersion" value="1.0.0" required>
                    </div>
                    <div class="form-group">
                        <label for="addScriptUrl">Script URL (Optional):</label>
                        <input type="url" id="addScriptUrl">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('addScriptModal')">Cancel</button>
                <button class="btn btn-primary" onclick="addScript()">Add Script</button>
            </div>
        </div>
    </div>

    <!-- Edit Script Modal -->
    <div id="editScriptModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Script</h3>
                <span class="close" onclick="closeModal('editScriptModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editScriptForm">
                    <input type="hidden" id="editScriptId">
                    <div class="form-group">
                        <label for="editStatus">Status:</label>
                        <select id="editStatus">
                            <option value="active" style="background-color: rgba(40, 167, 69, 0.2); color: #28a745;">Active</option>
                            <option value="disabled" style="background-color: rgba(220, 53, 69, 0.2); color: #dc3545;">Disabled</option>
                            <option value="maintenance" style="background-color: rgba(255, 193, 7, 0.2); color: #ffc107;">Maintenance</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editMaintenanceMessage">Maintenance Message:</label>
                        <textarea id="editMaintenanceMessage" rows="3" placeholder="Message shown during maintenance"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editAllowedUsers">Allowed Users (During Maintenance):</label>
                        <textarea id="editAllowedUsers" rows="2" placeholder="Enter HWIDs separated by commas"></textarea>
               </small>         <small>Users who can access the script during maintenance</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('editScriptModal')">Cancel</button>
                <button class="btn btn-primary" onclick="updateScript()">Update Script</button>
            </div>
        </div>
    </div>

    <!-- Bulk Operations Modal -->
    <div id="bulkModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Bulk Operations</h3>
                <span class="close" onclick="closeModal('bulkModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Select Scripts:</label>
                    <div id="scriptCheckboxes" style="max-height: 200px; overflow-y: auto; border: 1px solid var(--border-color); padding: 10px; border-radius: 4px;">
                        <!-- Script checkboxes will be populated here -->
                    </div>
                </div>
                <div class="form-group">
                    <label for="bulkAction">Action:</label>
                    <select id="bulkAction">
                        <option value="enable">Enable</option>
                        <option value="disable">Disable</option>
                        <option value="maintenance">Set to Maintenance</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('bulkModal')">Cancel</button>
                <button class="btn btn-primary" onclick="executeBulkOperation()">Execute</button>
            </div>
        </div>
    </div>

    <!-- System Settings Modal -->
    <div id="systemSettingsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>System Settings</h3>
                <span class="close" onclick="closeModal('systemSettingsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="heartbeatInterval">Heartbeat Interval (seconds):</label>
                    <input type="number" id="heartbeatInterval" min="10" max="300" value="30">
                    <small>How often scripts check in with the server</small>
                </div>
                <div class="form-group">
                    <label for="scriptTimeout">Script Timeout (seconds):</label>
                    <input type="number" id="scriptTimeout" min="60" max="3600" value="300">
                    <small>Maximum runtime for scripts</small>
                </div>
                <div class="form-group">
                    <label for="globalMaintenanceMessage">Global Maintenance Message:</label>
                    <textarea id="globalMaintenanceMessage" rows="3" placeholder="Message shown during system maintenance"></textarea>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableLogging"> Enable detailed logging
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('systemSettingsModal')">Cancel</button>
                <button class="btn btn-primary" onclick="saveSystemSettings()">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Upload Script Modal -->
    <div id="uploadScriptModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 700px; background: var(--bg-primary); border: 2px solid var(--border-color); box-shadow: 0 25px 50px var(--shadow-heavy);">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px 10px 0 0; padding: 20px;">
                <h3 style="margin: 0; font-size: 20px;">📤 Upload New Script</h3>
                <span class="close" onclick="closeModal('uploadScriptModal')" style="color: white; font-size: 24px; font-weight: bold; cursor: pointer;">&times;</span>
            </div>

            <!-- Action Buttons at Top -->
            <div style="padding: 25px; background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%); display: flex; gap: 15px; justify-content: center; border-bottom: 2px solid var(--border-color); margin-bottom: 0; box-shadow: 0 2px 10px var(--shadow-light);">
                <button class="btn btn-secondary" onclick="closeModal('uploadScriptModal')"
                        style="padding: 15px 30px; border: 2px solid var(--border-color); border-radius: 10px; background: var(--bg-primary); color: var(--text-primary); font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 14px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button class="btn btn-primary" onclick="uploadScript()"
                        style="padding: 15px 30px; border: none; border-radius: 10px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 14px; box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);">
                    <i class="fas fa-upload"></i> Upload Script
                </button>
            </div>

            <div class="modal-body" style="padding: 25px; background: var(--bg-primary);">
                <form id="uploadScriptForm">
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="uploadScriptName" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--text-primary); font-size: 14px;">Script Name <span style="color: #e74c3c;">*</span></label>
                        <input type="text" id="uploadScriptName" placeholder="My Awesome Script" required
                               style="width: 100%; padding: 12px 15px; border: 2px solid var(--border-color); border-radius: 8px; font-size: 14px; background: var(--bg-secondary); color: var(--text-primary); transition: border-color 0.3s ease;">
                    </div>
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="uploadScriptId" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--text-primary); font-size: 14px;">Script ID <span style="color: #e74c3c;">*</span></label>
                        <input type="text" id="uploadScriptId" placeholder="my-script-001" required
                               style="width: 100%; padding: 12px 15px; border: 2px solid var(--border-color); border-radius: 8px; font-size: 14px; background: var(--bg-secondary); color: var(--text-primary); transition: border-color 0.3s ease;">
                        <small style="color: var(--text-secondary); font-size: 12px; display: block; margin-top: 5px;">ℹ️ Use lowercase letters, numbers, and hyphens only</small>
                    </div>
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="uploadScriptDescription" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--text-primary); font-size: 14px;">Description</label>
                        <textarea id="uploadScriptDescription" rows="2" placeholder="What does this script do?"
                                  style="width: 100%; padding: 12px 15px; border: 2px solid var(--border-color); border-radius: 8px; font-size: 14px; background: var(--bg-secondary); color: var(--text-primary); resize: vertical; transition: border-color 0.3s ease;"></textarea>
                    </div>
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="uploadScriptVersion" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--text-primary); font-size: 14px;">Version</label>
                        <input type="text" id="uploadScriptVersion" value="1.0.0"
                               style="width: 100%; padding: 12px 15px; border: 2px solid var(--border-color); border-radius: 8px; font-size: 14px; background: var(--bg-secondary); color: var(--text-primary); transition: border-color 0.3s ease;">
                    </div>
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="scriptFileInput" style="display: block; margin-bottom: 12px; font-weight: 600; color: var(--text-primary); font-size: 16px;">Upload Script File <span style="color: #e74c3c;">*</span></label>
                        <div id="fileDropArea" style="position: relative; border: 3px dashed var(--border-color); border-radius: 15px; background: var(--bg-secondary); transition: all 0.3s ease; padding: 40px 20px; text-align: center; cursor: pointer;" onclick="document.getElementById('scriptFileInput').click()">
                            <div style="font-size: 48px; margin-bottom: 15px;">📄</div>
                            <div style="font-size: 18px; font-weight: 600; color: var(--text-primary); margin-bottom: 8px;">Drop your script file here</div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 15px;">or click to browse</div>
                            <input type="file" id="scriptFileInput" accept=".lua,.txt" required
                                   style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; cursor: pointer;">
                            <div id="fileSelectedInfo" style="display: none; margin-top: 10px; padding: 10px; background: var(--bg-primary); border-radius: 8px; color: var(--text-primary);">
                                <i class="fas fa-check-circle" style="color: #28a745; margin-right: 8px;"></i>
                                <span id="selectedFileName"></span>
                            </div>
                        </div>
                        <small style="color: var(--text-secondary); font-size: 12px; display: block; margin-top: 12px; text-align: center;">📁 Supports .lua and .txt files (Max 1MB)</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="padding: 20px 25px; background: var(--bg-secondary); border-radius: 0 0 10px 10px; text-align: center; color: var(--text-secondary); font-size: 12px;">
                💡 Tip: Make sure your script is tested before uploading!
            </div>
        </div>
    </div>



    <!-- Edit Script Content Modal -->
    <div id="editScriptContentModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3 id="editScriptContentTitle">Edit Script Content</h3>
                <span class="close" onclick="closeModal('editScriptContentModal')">&times;</span>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editContentScriptId">
                <div class="form-group">
                    <label>Script URL:</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="editScriptUrl" readonly style="flex: 1; background-color: var(--bg-tertiary); color: var(--text-primary);">
                        <button class="btn btn-secondary btn-sm" onclick="copyScriptUrl()">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                    </div>
                    <small>Use this URL in your loadstring: loadstring(game:HttpGet("URL"))()</small>
                </div>
                <div class="form-group">
                    <label for="editScriptCodeEditor">Script Content:</label>
                    <textarea id="editScriptCodeEditor" rows="20" style="font-family: 'Courier New', monospace; font-size: 12px; resize: vertical; background-color: var(--bg-tertiary); color: var(--text-primary);"></textarea>
                </div>
                <div style="display: flex; gap: 10px; margin-top: 10px;">
                    <button class="btn btn-secondary btn-sm" onclick="formatLuaCode()">
                        <i class="fas fa-code"></i> Format Code
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="validateLuaCode()">
                        <i class="fas fa-check-circle"></i> Validate
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('editScriptContentModal')">Cancel</button>
                <button class="btn btn-primary" onclick="adminDashboard.saveScriptContent()">Save Changes</button>
            </div>
        </div>
    </div>

    <!-- Script Details Modal -->
    <div id="scriptDetailsModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 id="scriptDetailsTitle">Script Details</h3>
                <span class="close" onclick="closeModal('scriptDetailsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>Script Information</h4>
                        <div id="scriptInfo"></div>
                    </div>
                    <div>
                        <h4>Usage Statistics</h4>
                        <div id="scriptStats"></div>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h4>Recent Activity</h4>
                    <div id="scriptActivity" style="max-height: 200px; overflow-y: auto;"></div>
                </div>
                <div style="margin-top: 20px;">
                    <h4>Active Sessions</h4>
                    <div id="scriptSessions" style="max-height: 200px; overflow-y: auto;"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('scriptDetailsModal')">Close</button>
                <button class="btn btn-primary" onclick="refreshScriptDetails()">Refresh</button>
            </div>
        </div>
    </div>

    <style>
        /* Kill Switch Specific Styles */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #dc3545;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #dc3545;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .slider.round {
            border-radius: 34px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        /* Dark/White Theme System */
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --text-accent: #495057;
            --border-color: #dee2e6;
            --shadow-light: rgba(0,0,0,0.1);
            --shadow-medium: rgba(0,0,0,0.15);
            --shadow-heavy: rgba(0,0,0,0.25);
            --hover-bg: #f0f0f0;
        }

        [data-theme="dark"] {
            --bg-primary: #0d1117;
            --bg-secondary: #161b22;
            --bg-tertiary: #21262d;
            --text-primary: #f0f6fc;
            --text-secondary: #8b949e;
            --text-accent: #c9d1d9;
            --border-color: #30363d;
            --shadow-light: rgba(0,0,0,0.4);
            --shadow-medium: rgba(0,0,0,0.5);
            --shadow-heavy: rgba(0,0,0,0.7);
            --hover-bg: #262c36;
        }

        body {
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .admin-container {
            background: var(--bg-primary);
        }

        .sidebar {
            background: var(--bg-primary);
            border-right: 1px solid var(--border-color);
        }

        .main-content {
            background: var(--bg-secondary);
        }

        /* Input Focus Styles */
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
            background: var(--bg-primary) !important;
        }

        /* Better visibility for placeholders */
        input::placeholder, textarea::placeholder {
            color: var(--text-secondary);
            opacity: 0.7;
        }

        [data-theme="dark"] input::placeholder,
        [data-theme="dark"] textarea::placeholder {
            color: #8b949e;
            opacity: 0.8;
        }

        /* Modern Script Manager Styles */
        .modern-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .modern-stat-card {
            padding: 25px;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow: 0 15px 35px var(--shadow-light);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .modern-stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px var(--shadow-medium);
        }

        .modern-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.1);
            pointer-events: none;
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .stat-content h3 {
            font-size: 28px;
            margin: 0 0 5px 0;
            font-weight: 700;
        }

        .stat-content p {
            margin: 0;
            opacity: 0.9;
            font-weight: 500;
        }

        .stat-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .modern-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }

        .modern-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255,255,255,0.3);
            transition: .3s;
            border-radius: 28px;
            border: 2px solid rgba(255,255,255,0.5);
        }

        .switch-slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .3s;
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        input:checked + .switch-slider {
            background-color: rgba(255,255,255,0.8);
        }

        input:checked + .switch-slider:before {
            transform: translateX(22px);
        }

        .modern-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .modern-btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .modern-btn:hover:before {
            left: 100%;
        }

        .modern-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .modern-btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4);
        }

        .modern-btn-warning {
            background: linear-gradient(135deg, #FFB75E 0%, #ED8F03 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 183, 94, 0.4);
        }

        .modern-btn-danger {
            background: linear-gradient(135deg, #D31027 0%, #EA384D 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(211, 16, 39, 0.4);
        }

        .modern-btn-secondary {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.4);
        }

        .modern-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .scripts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
        }

        .script-card {
            background: var(--bg-primary);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 25px var(--shadow-light);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .script-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px var(--shadow-medium);
        }

        .script-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .script-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 5px 0;
        }

        .script-id {
            font-size: 12px;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
            background: var(--bg-tertiary);
            padding: 4px 8px;
            border-radius: 6px;
            display: inline-block;
        }

        .script-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .status-disabled {
            background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
            color: white;
        }

        .status-maintenance {
            background: linear-gradient(135deg, #FFB75E 0%, #ED8F03 100%);
            color: white;
        }

        .script-info {
            margin-bottom: 20px;
        }

        .script-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .script-info-item:last-child {
            border-bottom: none;
        }

        .script-info-label {
            font-size: 13px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .script-info-value {
            font-size: 13px;
            color: var(--text-primary);
            font-weight: 600;
        }

        .script-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .script-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .script-btn:hover {
            transform: translateY(-1px);
        }

        .script-btn-edit {
            background: #3182ce;
            color: white;
        }

        .script-btn-delete {
            background: #e53e3e;
            color: white;
        }

        .script-btn-copy {
            background: #38a169;
            color: white;
        }

        .script-btn-view {
            background: #805ad5;
            color: white;
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 0.75rem;
            min-height: 30px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: var(--bg-primary);
            border-radius: 15px;
            border: 2px dashed var(--border-color);
        }

        /* Theme Toggle Button */
        .theme-toggle {
            background: var(--bg-primary);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            padding: 8px 15px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
            box-shadow: 0 4px 15px var(--shadow-light);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            height: fit-content;
            white-space: nowrap;
        }

        .theme-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-medium);
            background: var(--bg-secondary);
        }

        /* Modal Improvements */
        .modal {
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        [data-theme="dark"] .modal {
            background: rgba(0, 0, 0, 0.8);
        }

        .modal input:focus,
        .modal textarea:focus,
        .modal select:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
            background: var(--bg-primary) !important;
        }

        /* Enhanced File input styling */
        .form-group div[onclick]:hover {
            border-color: #667eea !important;
            background: var(--bg-tertiary) !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--shadow-medium);
        }

        .form-group div[onclick]:active {
            transform: translateY(0);
        }

        /* Drag and drop states */
        #fileDropArea.drag-over {
            border-color: #28a745 !important;
            background: var(--bg-hover) !important;
            transform: scale(1.02);
            box-shadow: 0 10px 30px var(--shadow-heavy);
        }

        #fileDropArea.drag-over div:first-child {
            animation: bounce 0.6s ease-in-out infinite alternate;
        }

        @keyframes bounce {
            from { transform: translateY(0px); }
            to { transform: translateY(-10px); }
        }

        /* Better button contrast */
        .btn-secondary {
            background: var(--bg-tertiary) !important;
            color: var(--text-primary) !important;
            border: 1px solid var(--border-color) !important;
        }

        .btn-secondary:hover {
            background: var(--hover-bg) !important;
            border-color: #667eea !important;
        }

        @media (max-width: 768px) {
            .scripts-grid {
                grid-template-columns: 1fr;
            }

            .modern-stats-grid {
                grid-template-columns: 1fr;
            }

            .script-actions {
                gap: 8px;
            }

            .modern-action-bar > div {
                flex-wrap: wrap;
            }

            .theme-toggle {
                font-size: 11px;
                padding: 6px 12px;
                gap: 4px;
            }

            .theme-toggle #themeText {
                display: none;
            }
        }

        /* Prominent button hover effects */
        .prominent-upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
            background: #34a853 !important;
        }

        .prominent-refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4) !important;
            background: #20a2c4 !important;
        }

        /* Modal top action buttons hover effects */
        #uploadScriptModal .btn-secondary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(0,0,0,0.5) !important;
            background: var(--bg-hover) !important;
            border-color: var(--border-light) !important;
        }

        #uploadScriptModal .btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6) !important;
            background: linear-gradient(135deg, #34ce57 0%, #11998e 100%) !important;
        }

        /* Mobile responsive styles for prominent buttons */
        @media (max-width: 600px) {
            .prominent-upload-btn,
            .prominent-refresh-btn {
                font-size: 14px !important;
                padding: 12px 20px !important;
                flex: 1;
                min-width: 0;
            }

            .prominent-upload-btn i,
            .prominent-refresh-btn i {
                margin-right: 6px;
            }

            /* Modal top buttons responsive */
            #uploadScriptModal .btn-secondary,
            #uploadScriptModal .btn-primary {
                padding: 12px 20px !important;
                font-size: 13px !important;
                flex: 1;
            }

            #uploadScriptModal div[style*="justify-content: center"] {
                flex-direction: column !important;
                gap: 10px !important;
            }
        }

        @media (max-width: 400px) {
            .prominent-upload-btn,
            .prominent-refresh-btn {
                font-size: 13px !important;
                padding: 10px 15px !important;
            }

            .prominent-upload-btn span:not(.fas),
            .prominent-refresh-btn span:not(.fas) {
                display: none;
            }

            /* Modal top buttons very small screens */
            #uploadScriptModal .btn-secondary,
            #uploadScriptModal .btn-primary {
                padding: 10px 15px !important;
                font-size: 12px !important;
            }
        }

        .status-active {
            color: #28a745;
            font-weight: bold;
            background-color: rgba(40, 167, 69, 0.15);
            padding: 3px 8px;
            border-radius: 4px;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status-disabled {
            color: #dc3545;
            font-weight: bold;
            background-color: rgba(220, 53, 69, 0.15);
            padding: 3px 8px;
            border-radius: 4px;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .status-maintenance {
            color: #ffc107;
            font-weight: bold;
            background-color: rgba(255, 193, 7, 0.15);
            padding: 3px 8px;
            border-radius: 4px;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: var(--bg-primary);
            margin: 5% auto;
            padding: 0;
            border: 1px solid var(--border-color);
            width: 90%;
            max-width: 500px;
            border-radius: 8px;
            color: var(--text-primary);
            box-shadow: 0 15px 35px var(--shadow-heavy);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #fff;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: var(--text-primary);
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
            outline: none;
        }

        .form-group small {
            display: block;
            margin-top: 5px;
            color: #999;
            font-size: 12px;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            transition: all 0.2s ease;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #218838 0%, #1aa179 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border: none;
            transition: all 0.2s ease;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e0a800 0%, #e76b00 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            border: none;
            transition: all 0.2s ease;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #d83384 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            transition: all 0.2s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
        }
    </style>

    <script>
        // Kill Switch JavaScript Functions

        // Additional script management functions
        function showBulkModal() {
            if (window.adminDashboard && window.adminDashboard.scripts) {
                const checkboxContainer = document.getElementById('scriptCheckboxes');
                checkboxContainer.innerHTML = window.adminDashboard.scripts.map(script =>
                    `<label style="display: block; margin: 5px 0;">
                        <input type="checkbox" value="${script.script_id}" style="margin-right: 8px;">
                        ${script.script_name} (${script.script_id})
                    </label>`
                ).join('');
            }
            document.getElementById('bulkModal').style.display = 'block';
        }

        function showUploadScriptModal() {
            const modal = document.getElementById('uploadScriptModal');
            modal.style.display = 'block';

            // Clear form
            document.getElementById('uploadScriptForm').reset();

            // Focus on name field
            setTimeout(() => {
                document.getElementById('uploadScriptName').focus();
            }, 100);
        }

        function refreshScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.loadScripts();
            }
        }

        function enableAllScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.enableAllScripts();
            }
        }

        function maintenanceAllScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.maintenanceAllScripts();
            }
        }

        function disableAllScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.disableAllScripts();
            }
        }

        function showAddScriptModal() {
            // For now, just redirect to upload modal
            showUploadScriptModal();
        }

        function exportScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.exportScripts();
            }
        }

        function showBulkModal() {
            if (window.adminDashboard) {
                window.adminDashboard.showBulkModal();
            } else {
                document.getElementById('bulkModal').style.display = 'block';
            }
        }

        function executeBulkOperation() {
            if (window.adminDashboard) {
                window.adminDashboard.executeBulkOperation();
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function addScript() {
            if (window.adminDashboard) {
                window.adminDashboard.addScript();
            }
        }

        function updateScript() {
            if (window.adminDashboard) {
                window.adminDashboard.updateScript();
            }
        }

        function saveSystemSettings() {
            if (window.adminDashboard) {
                window.adminDashboard.saveSystemSettings();
            }
        }

        function copyScriptUrl() {
            if (window.adminDashboard) {
                window.adminDashboard.copyScriptUrl();
            }
        }

        function formatLuaCode() {
            if (window.adminDashboard) {
                window.adminDashboard.formatLuaCode();
            }
        }

        function validateLuaCode() {
            if (window.adminDashboard) {
                window.adminDashboard.validateLuaCode();
            }
        }

        function filterScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.filterScripts();
            }
        }

        function clearScriptFilters() {
            document.getElementById('scriptSearchInput').value = '';
            document.getElementById('statusFilter').value = 'all';
            if (window.adminDashboard) {
                window.adminDashboard.updateScriptsGrid();
            }
        }

        // Theme Toggle Functionality
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Update theme toggle button
            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');

            if (newTheme === 'dark') {
                themeIcon.textContent = '☀️';
                themeText.textContent = 'Light Mode';
            } else {
                themeIcon.textContent = '🌙';
                themeText.textContent = 'Dark Mode';
            }
        }

        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const themeIcon = document.getElementById('themeIcon');
            const themeText = document.getElementById('themeText');

            if (savedTheme === 'dark') {
                themeIcon.textContent = '☀️';
                themeText.textContent = 'Light Mode';
            } else {
                themeIcon.textContent = '🌙';
                themeText.textContent = 'Dark Mode';
            }
        });

        // File input handler - set up once when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('scriptFileInput');
            const dropArea = document.getElementById('fileDropArea');

            function handleFile(file) {
                if (file.size > 1024 * 1024) { // 1MB limit
                    alert('File is too large. Please select a file smaller than 1MB.');
                    return false;
                }

                if (!file.name.match(/\.(lua|txt)$/i)) {
                    alert('Please select a .lua or .txt file.');
                    return false;
                }

                // Show file selected info
                const fileInfo = document.getElementById('fileSelectedInfo');
                const fileName = document.getElementById('selectedFileName');
                if (fileInfo && fileName) {
                    fileName.textContent = file.name;
                    fileInfo.style.display = 'block';
                }

                // Auto-fill script name and ID if empty
                const nameInput = document.getElementById('uploadScriptName');
                const idInput = document.getElementById('uploadScriptId');

                if (!nameInput.value) {
                    const baseName = file.name.replace(/\.[^/.]+$/, "");
                    nameInput.value = baseName;
                }

                if (!idInput.value) {
                    const baseName = file.name.replace(/\.[^/.]+$/, "");
                    const scriptId = baseName.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
                    idInput.value = scriptId;
                }

                return true;
            }

            if (fileInput) {
                fileInput.addEventListener('change', function(event) {
                    const file = event.target.files[0];
                    if (file) {
                        handleFile(file);
                    }
                });
            }

            // Drag and drop functionality
            if (dropArea) {
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    dropArea.addEventListener(eventName, preventDefaults, false);
                });

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                ['dragenter', 'dragover'].forEach(eventName => {
                    dropArea.addEventListener(eventName, highlight, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    dropArea.addEventListener(eventName, unhighlight, false);
                });

                function highlight(e) {
                    dropArea.classList.add('drag-over');
                }

                function unhighlight(e) {
                    dropArea.classList.remove('drag-over');
                }

                dropArea.addEventListener('drop', handleDrop, false);

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;

                    if (files.length > 0) {
                        const file = files[0];
                        if (handleFile(file)) {
                            // Set the file to the input
                            const dataTransfer = new DataTransfer();
                            dataTransfer.items.add(file);
                            fileInput.files = dataTransfer.files;
                        }
                    }
                }
            }
        });

        function showSystemSettingsModal() {
            if (window.adminDashboard && window.adminDashboard.systemSettings) {
                document.getElementById('heartbeatInterval').value = window.adminDashboard.systemSettings.heartbeat_interval || 30;
                document.getElementById('scriptTimeout').value = window.adminDashboard.systemSettings.script_timeout || 300;
                document.getElementById('globalMaintenanceMessage').value = window.adminDashboard.systemSettings.maintenance_message || '';
            }
            document.getElementById('systemSettingsModal').style.display = 'block';
        }

        async function executeBulkOperation() {
            const checkboxes = document.querySelectorAll('#scriptCheckboxes input[type="checkbox"]:checked');
            const scriptIds = Array.from(checkboxes).map(cb => cb.value);
            const action = document.getElementById('bulkAction').value;

            if (scriptIds.length === 0) {
                if (window.adminDashboard) {
                    window.adminDashboard.showError('Please select at least one script');
                }
                return;
            }

            if (!confirm(`Are you sure you want to ${action} ${scriptIds.length} script(s)?`)) {
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/app/admin/scripts/bulk', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
                    },
                    body: JSON.stringify({
                        action: action,
                        script_ids: scriptIds
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to execute bulk operation');
                }

                if (window.adminDashboard) {
                    window.adminDashboard.showSuccess(`Bulk ${action} operation completed successfully`);
                    window.adminDashboard.loadScripts();
                }
                closeModal('bulkModal');
            } catch (error) {
                if (window.adminDashboard) {
                    window.adminDashboard.showError('Failed to execute bulk operation: ' + error.message);
                }
            }
        }

        async function saveSystemSettings() {
            const heartbeatInterval = document.getElementById('heartbeatInterval').value;
            const scriptTimeout = document.getElementById('scriptTimeout').value;
            const maintenanceMessage = document.getElementById('globalMaintenanceMessage').value;

            if (!window.adminDashboard) return;

            try {
                await window.adminDashboard.updateSystemSetting('heartbeat_interval', parseInt(heartbeatInterval));
                await window.adminDashboard.updateSystemSetting('script_timeout', parseInt(scriptTimeout));
                await window.adminDashboard.updateSystemSetting('maintenance_message', maintenanceMessage);

                window.adminDashboard.showSuccess('System settings updated successfully');
                closeModal('systemSettingsModal');
            } catch (error) {
                window.adminDashboard.showError('Failed to update system settings: ' + error.message);
            }
        }

        async function viewScriptDetails(scriptId) {
            if (!window.adminDashboard) return;

            try {
                const script = window.adminDashboard.scripts.find(s => s.script_id === scriptId);
                if (!script) return;

                document.getElementById('scriptDetailsTitle').textContent = `${script.script_name} Details`;

                document.getElementById('scriptInfo').innerHTML = `
                    <p><strong>Script ID:</strong> ${script.script_id}</p>
                    <p><strong>Version:</strong> ${script.version}</p>
                    <p><strong>Status:</strong> <span class="status-${script.status}">${script.status.toUpperCase()}</span></p>
                    <p><strong>Description:</strong> ${script.description || 'No description'}</p>
                    <p><strong>Created:</strong> ${new Date(script.created_at).toLocaleString()}</p>
                    <p><strong>Last Updated:</strong> ${new Date(script.updated_at).toLocaleString()}</p>
                `;

                const analytics = await window.adminDashboard.getScriptAnalytics(scriptId);
                if (analytics) {
                    document.getElementById('scriptStats').innerHTML = `
                        <p><strong>Total Usage:</strong> ${analytics.total}</p>
                        <p><strong>Today:</strong> ${analytics.today}</p>
                        <p><strong>This Week:</strong> ${analytics.thisWeek}</p>
                        <p><strong>Blocked:</strong> ${analytics.blocked}</p>
                    `;
                }

                const logs = await window.adminDashboard.getScriptLogs(scriptId, 10);
                document.getElementById('scriptActivity').innerHTML = logs.map(log =>
                    `<div style="border-bottom: 1px solid var(--border-color); padding: 5px 0;">
                        <strong>${log.action}</strong> - ${log.status}
                        <small>(${new Date(log.created_at).toLocaleString()})</small>
                        ${log.error_message ? `<br><span style="color: #dc3545;">${log.error_message}</span>` : ''}
                    </div>`
                ).join('');

                const sessions = await window.adminDashboard.getScriptSessions(scriptId);
                document.getElementById('scriptSessions').innerHTML = sessions.length > 0 ?
                    sessions.map(session =>
                        `<div style="border-bottom: 1px solid var(--border-color); padding: 5px 0;">
                            <strong>Session:</strong> ${session.session_token.substring(0, 8)}...
                            <small>(Started: ${new Date(session.started_at).toLocaleString()})</small>
                            <button class="btn btn-danger btn-sm" onclick="terminateSession('${session.session_token}')" style="float: right;">
                                Terminate
                            </button>
                        </div>`
                    ).join('') : '<p>No active sessions</p>';

                document.getElementById('scriptDetailsModal').style.display = 'block';
            } catch (error) {
                window.adminDashboard.showError('Failed to load script details: ' + error.message);
            }
        }

        async function terminateSession(sessionToken) {
            if (!confirm('Are you sure you want to terminate this session?')) return;

            try {
                const response = await fetch('/.netlify/functions/app/admin/scripts/terminate-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
                    },
                    body: JSON.stringify({
                        session_token: sessionToken
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to terminate session');
                }

                if (window.adminDashboard) {
                    window.adminDashboard.showSuccess('Session terminated successfully');
                }
                // Refresh the script details
                refreshScriptDetails();
            } catch (error) {
                if (window.adminDashboard) {
                    window.adminDashboard.showError('Failed to terminate session: ' + error.message);
                }
            }
        }

        function refreshScriptDetails() {
            const modal = document.getElementById('scriptDetailsModal');
            if (modal.style.display === 'block') {
                // Get the script ID from the title or store it when opening
                const scripts = window.adminDashboard.scripts;
                if (scripts && scripts.length > 0) {
                    // Re-open details for the first script as example - should store current script ID
                    viewScriptDetails(scripts[0].script_id);
                }
            }
        }

        async function deleteScript(scriptId) {
            if (!window.adminDashboard) return;

            if (!confirm('Are you sure you want to delete this script? This action cannot be undone.')) return;

            try {
                await window.adminDashboard.deleteScript(scriptId);
            } catch (error) {
                // Error already handled in deleteScript method
            }
        }

        function exportScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.exportScripts();
            }
        }

        // Global script management functions for HTML onclick events
        function toggleGlobalKillSwitch() {
            if (window.adminDashboard) {
                window.adminDashboard.toggleGlobalKillSwitch();
            }
        }

        function toggleMaintenanceMode() {
            if (window.adminDashboard) {
                window.adminDashboard.toggleMaintenanceMode();
            }
        }

        function showAddScriptModal() {
            document.getElementById('addScriptModal').style.display = 'block';
        }

        function refreshScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.loadScripts();
            }
        }

        function enableAllScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.enableAllScripts();
            }
        }

        function maintenanceAllScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.maintenanceAllScripts();
            }
        }

        function disableAllScripts() {
            if (window.adminDashboard) {
                window.adminDashboard.disableAllScripts();
            }
        }

        function editScript(scriptId) {
            if (window.adminDashboard) {
                window.adminDashboard.editScript(scriptId);
            }
        }

        function updateScript() {
            if (window.adminDashboard) {
                window.adminDashboard.updateScript();
            }
        }

        function updateScriptStatus(scriptId, status) {
            if (window.adminDashboard) {
                window.adminDashboard.updateScriptStatus(scriptId, status);
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        async function addScript() {
            const name = document.getElementById('scriptName').value;
            const id = document.getElementById('scriptId').value;
            const description = document.getElementById('scriptDescription').value;
            const version = document.getElementById('scriptVersion').value;
            const url = document.getElementById('scriptUrl').value;

            if (!name || !id || !version) {
                if (window.adminDashboard) {
                    window.adminDashboard.showError('Please fill in all required fields');
                }
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/app/admin/scripts/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
                    },
                    body: JSON.stringify({
                        script_name: name,
                        script_id: id,
                        description: description,
                        version: version,
                        script_url: url
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to add script');
                }

                if (window.adminDashboard) {
                    window.adminDashboard.showSuccess('Script added successfully');
                    window.adminDashboard.loadScripts();
                }
                closeModal('addScriptModal');
                document.getElementById('addScriptForm').reset();
            } catch (error) {
                if (window.adminDashboard) {
                    window.adminDashboard.showError('Failed to add script: ' + error.message);
                }
            }
        }

        // Input validation helpers
        document.addEventListener('DOMContentLoaded', function() {
            // Script ID validation
            const scriptIdInput = document.getElementById('uploadScriptId');
            if (scriptIdInput) {
                scriptIdInput.addEventListener('input', function(e) {
                    let value = e.target.value;
                    // Convert to lowercase and replace invalid chars with hyphens
                    value = value.toLowerCase().replace(/[^a-z0-9\-]/g, '-');
                    // Remove multiple consecutive hyphens
                    value = value.replace(/-+/g, '-');
                    // Remove leading/trailing hyphens
                    value = value.replace(/^-|-$/g, '');
                    e.target.value = value;
                });
            }

        });

        async function uploadScript() {
            const name = document.getElementById('uploadScriptName').value.trim();
            const id = document.getElementById('uploadScriptId').value.trim();
            const description = document.getElementById('uploadScriptDescription').value.trim();
            const version = document.getElementById('uploadScriptVersion').value.trim();
            const fileInput = document.getElementById('scriptFileInput');

            // Validation
            if (!name) {
                alert('Please enter a script name');
                document.getElementById('uploadScriptName').focus();
                return;
            }
            if (!id) {
                alert('Please enter a script ID');
                document.getElementById('uploadScriptId').focus();
                return;
            }
            if (!fileInput.files[0]) {
                alert('Please select a script file to upload');
                fileInput.focus();
                return;
            }

            // Validate script ID format
            if (!/^[a-z0-9\-]+$/.test(id)) {
                alert('Script ID can only contain lowercase letters, numbers, and hyphens');
                document.getElementById('uploadScriptId').focus();
                return;
            }

            // Show loading state
            const uploadBtn = document.querySelector('#uploadScriptModal .btn-primary');
            const originalText = uploadBtn.innerHTML;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
            uploadBtn.disabled = true;

            try {
                console.log('Starting upload...');
                const token = localStorage.getItem('admin_token');
                if (!token) {
                    throw new Error('No admin token found. Please login again.');
                }

                // Read file content
                const file = fileInput.files[0];
                const content = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => resolve(e.target.result);
                    reader.onerror = (e) => reject(new Error('Failed to read file'));
                    reader.readAsText(file);
                });

                const requestData = {
                    script_name: name,
                    script_id: id,
                    script_content: content,
                    description: description || '',
                    version: version || '1.0.0'
                };

                console.log('Upload data:', requestData);

                const response = await fetch('/.netlify/functions/app/admin/scripts/upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);

                if (!response.ok) {
                    throw new Error(data.error || `Server error: ${response.status}`);
                }

                // Success
                alert('Script uploaded successfully!\n\nScript URL: ' + data.script_url);

                if (window.adminDashboard) {
                    window.adminDashboard.loadScripts();
                }

                closeModal('uploadScriptModal');
                document.getElementById('uploadScriptForm').reset();

            } catch (error) {
                console.error('Upload error:', error);
                alert('Failed to upload script:\n' + error.message);
            } finally {
                // Reset button state
                uploadBtn.innerHTML = originalText;
                uploadBtn.disabled = false;
            }
        }

        async function editScriptContent(scriptId) {
            // Use the class method if available
            if (window.adminDashboard) {
                window.adminDashboard.editScriptContent(scriptId);
            } else {
                try {
                    const response = await fetch(`/.netlify/functions/app/admin/scripts/content?script_id=${scriptId}`, {
                        headers: { 'Authorization': `Bearer ${localStorage.getItem('admin_token')}` }
                    });

                    if (!response.ok) {
                        throw new Error('Failed to load script content');
                    }

                    const data = await response.json();

                    document.getElementById('editContentScriptId').value = scriptId;
                    document.getElementById('editScriptUrl').value = data.script_url;
                    document.getElementById('editScriptCodeEditor').value = data.content || '-- No content available';
                    document.getElementById('editScriptContentTitle').textContent = `Edit Script: ${scriptId}`;

                    document.getElementById('editScriptContentModal').style.display = 'block';
                } catch (error) {
                    alert('Failed to load script content: ' + error.message);
                }
            }
        }

        async function saveScriptContent() {
            // Use the class method instead
            if (window.adminDashboard) {
                window.adminDashboard.saveScriptContent();
            } else {
                try {
                    const scriptId = document.getElementById('editContentScriptId').value;
                    const content = document.getElementById('editScriptCodeEditor').value;

                    if (!content.trim()) {
                        alert('Script content cannot be empty');
                        return;
                    }

                    const response = await fetch('/.netlify/functions/app/admin/scripts/update-content', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
                        },
                        body: JSON.stringify({
                            script_id: scriptId,
                            script_content: content
                        })
                    });

                    if (!response.ok) {
                        throw new Error('Failed to save script content');
                    }

                    alert('Script content saved successfully');
                    closeModal('editScriptContentModal');
                } catch (error) {
                    alert('Failed to save script content: ' + error.message);
                }
            }
        }

        function copyScriptUrl() {
            const urlInput = document.getElementById('editScriptUrl');
            urlInput.select();
            urlInput.setSelectionRange(0, 99999);
            document.execCommand('copy');

            if (window.adminDashboard) {
                window.adminDashboard.showSuccess('Script URL copied to clipboard!');
            }
        }

        function formatLuaCode() {
            const editor = document.getElementById('editScriptCodeEditor');
            let code = editor.value;

            // Basic Lua formatting
            code = code.replace(/\t/g, '    '); // Replace tabs with spaces
            code = code.replace(/\s+$/gm, ''); // Remove trailing whitespace
            code = code.replace(/\n{3,}/g, '\n\n'); // Remove excessive newlines

            editor.value = code;

            if (window.adminDashboard) {
                window.adminDashboard.showSuccess('Code formatted');
            }
        }

        function validateLuaCode() {
            const content = document.getElementById('editScriptCodeEditor').value;

            // Basic Lua syntax validation
            const issues = [];

            if (!content.trim()) {
                issues.push('Script is empty');
            }

            // Check for balanced parentheses, brackets, etc.
            const openParens = (content.match(/\(/g) || []).length;
            const closeParens = (content.match(/\)/g) || []).length;
            if (openParens !== closeParens) {
                issues.push('Unmatched parentheses');
            }

            const openBrackets = (content.match(/\[/g) || []).length;
            const closeBrackets = (content.match(/\]/g) || []).length;
            if (openBrackets !== closeBrackets) {
                issues.push('Unmatched brackets');
            }

            const openBraces = (content.match(/\{/g) || []).length;
            const closeBraces = (content.match(/\}/g) || []).length;
            if (openBraces !== closeBraces) {
                issues.push('Unmatched braces');
            }

            if (issues.length === 0) {
                if (window.adminDashboard) {
                    window.adminDashboard.showSuccess('✅ Code validation passed!');
                }
            } else {
                if (window.adminDashboard) {
                    window.adminDashboard.showError('❌ Validation issues: ' + issues.join(', '));
                }
            }
        }
    </script>

</body>
</html>
