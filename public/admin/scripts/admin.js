class AdminDashboard {
  constructor() {
    this.currentUser = null;
    this.currentSection = "dashboard";
    this.apiBase = "/.netlify/functions/admin";
    this.isMobile = window.innerWidth <= 768;
    this.sidebarOpen = false;
    this.init();
  }

  async init() {
    if (!(await this.checkAuth())) {
      window.location.href = "/admin/login.html";
      return;
    }

    this.initializeUI();
    this.setupEventListeners();
    this.setupMobileMenu();
    await this.loadDashboardData();
    this.startAutoRefresh();
  }

  async checkAuth() {
    const token = localStorage.getItem("admin_token");
    if (!token) return false;

    try {
      const response = await fetch(`${this.apiBase}/me`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();

        this.currentUser = {
          username: data.username,
          role: data.role,
        };

        const adminInfo = document.querySelector(".admin-info span");
        if (adminInfo) {
          adminInfo.textContent = `Welcome, ${this.currentUser.username}`;
        }

        return true;
      } else {
        throw new Error("Invalid token");
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      localStorage.removeItem("admin_token");
      localStorage.removeItem("admin_user");
      localStorage.removeItem("admin_auth_time");
      return false;
    }
  }

  initializeUI() {
    document.querySelectorAll(".sidebar-menu li").forEach((item) => {
      item.addEventListener("click", () => {
        const section = item.dataset.section;
        this.switchSection(section);
      });
    });

    document.querySelectorAll(".search-bar input").forEach((input) => {
      input.addEventListener(
        "input",
        this.debounce((e) => {
          this.handleSearch(e.target.id, e.target.value);
        }, 300),
      );
    });

    this.initializeSettings();
    this.setupResizeHandler();
  }

  setupEventListeners() {
    // Quick action buttons
    document.querySelectorAll(".action-card").forEach((card) => {
      card.addEventListener("click", () => {
        const action = card.dataset.action;
        switch (action) {
          case "generateNewKeys":
            this.generateNewKeys();
            break;
          case "cleanupExpiredKeys":
            this.cleanupExpiredKeys();
            break;
          case "switchToKeys":
            this.switchSection("keys");
            break;
          case "switchToUsers":
            this.switchSection("users");
            break;
          case "showExpiredKeys":
            this.showExpiredKeys();
            break;
        }
      });
    });

    // Mobile menu toggle
    const mobileMenuBtn = document.querySelector(".mobile-menu-btn");
    const mobileOverlay = document.querySelector(".mobile-overlay");

    if (mobileMenuBtn) {
      mobileMenuBtn.addEventListener("click", () => this.toggleMobileMenu());
    }

    if (mobileOverlay) {
      mobileOverlay.addEventListener("click", () => this.closeMobileMenu());
    }

    const saveSettingsBtn = document.getElementById("save-settings");
    if (saveSettingsBtn) {
      saveSettingsBtn.addEventListener("click", () => this.saveSettings());
    }

    document.addEventListener("click", (e) => {
      if (
        e.target.dataset.action === "showExpiredKeys" ||
        e.target.closest('[data-action="showExpiredKeys"]')
      ) {
        this.showExpiredKeys();
      }
    });

    document.addEventListener("click", (e) => {
      const action =
        e.target.dataset.action ||
        e.target.closest("[data-action]")?.dataset.action;
      if (action) {
        switch (action) {
          case "loadKeys":
            this.loadKeys();
            break;
          case "generateNewKeys":
            this.generateNewKeys();
            break;
          case "cleanupExpiredKeys":
            this.cleanupExpiredKeys();
            break;
          case "refreshDashboard":
            this.loadDashboardData();
            break;
          case "refreshHealth":
            this.loadDashboardData();
            break;
          case "loadUsers":
            this.loadUsers();
            break;
          case "loadLogs":
            this.loadLogs();
            break;
          case "clearLogs":
            this.clearLogs();
            break;
          case "logout":
            this.logout();
            break;
        }
      }
    });

    // Analytics period selector
    const analyticsPeriod = document.getElementById("analytics-period");
    if (analyticsPeriod) {
      analyticsPeriod.addEventListener("change", (e) => {
        this.loadAnalytics(e.target.value);
      });
    }
  }

  async loadDashboardData() {
    try {
      document.getElementById("page-title").textContent = "Dashboard";
      document.getElementById("page-subtitle").textContent = "System Overview";

      const response = await fetch(`${this.apiBase}/stats`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        this.updateDashboardStats(data.stats);
        this.updateRecentActivity(data.recentKeys);
      } else {
        throw new Error("Failed to load dashboard data");
      }
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
      this.showError("Failed to load dashboard data");
    }
  }

  updateDashboardStats(stats) {
    const totalKeysEl = document.getElementById("total-keys");
    const activeKeysStatEl = document.getElementById("active-keys-stat");
    const todayKeysEl = document.getElementById("today-keys");
    const todayKeysStatEl = document.getElementById("today-keys-stat");
    const todayUsersEl = document.getElementById("today-users");
    const expiredKeysEl = document.getElementById("expired-keys");

    if (totalKeysEl) totalKeysEl.textContent = stats.totalKeys.toLocaleString();
    if (activeKeysStatEl)
      activeKeysStatEl.textContent = stats.activeKeys.toLocaleString();
    if (todayKeysEl) todayKeysEl.textContent = stats.todayKeys.toLocaleString();
    if (todayKeysStatEl)
      todayKeysStatEl.textContent = stats.todayKeys.toLocaleString();
    if (todayUsersEl)
      todayUsersEl.textContent = stats.todayKeys.toLocaleString();
    if (expiredKeysEl) {
      expiredKeysEl.textContent = stats.expiredKeys.toLocaleString();
      // Visual warning for many expired keys
      if (stats.expiredKeys > 10) {
        expiredKeysEl.style.color = "#ff6b6b";
        expiredKeysEl.style.fontWeight = "bold";
      }
    }

    // Update stat cards with trends
    const stats_cards = document.querySelectorAll(".stat-card");
    stats_cards.forEach((card) => {
      const trend = card.querySelector(".stat-trend");
      if (trend) {
        const isPositive = stats.usageRate > 50;
        trend.innerHTML = `
                    <i class="fas fa-arrow-${isPositive ? "up" : "down"}"></i>
                    <span>${isPositive ? "+" : "-"}${stats.usageRate}%</span>
                `;
        trend.style.color = isPositive ? "#16a34a" : "#dc2626";
      }
    });

    // Update expired keys quick stat with warning color
    const expiredQuickStat = document.querySelector(
      ".quick-stat .quick-stat-number#expired-keys",
    ).parentElement;
    if (expiredQuickStat && stats.expiredKeys > 0) {
      expiredQuickStat.style.background = "rgba(255, 107, 107, 0.1)";
      expiredQuickStat.style.border = "1px solid rgba(255, 107, 107, 0.3)";
    }
  }

  updateRecentActivity(recentKeys) {
    const container = document.getElementById("recent-keys");
    if (!container || !recentKeys) return;

    const now = new Date();
    container.innerHTML = recentKeys
      .map((key) => {
        const expiresAt = new Date(key.expires_at);
        const isExpired = expiresAt < now;
        const timeUntilExpiry = expiresAt - now;
        const hoursUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60 * 60));

        let iconClass = "fas fa-key";
        let titleText = "Key Generated";
        let statusIndicator = "";

        if (isExpired) {
          iconClass = "fas fa-exclamation-triangle";
          titleText = "Key Generated (EXPIRED)";
          statusIndicator =
            '<small style="color: #ff6b6b; font-weight: bold;">EXPIRED</small>';
        } else if (hoursUntilExpiry <= 24) {
          iconClass = "fas fa-clock";
          titleText = "Key Generated (Expiring Soon)";
          statusIndicator = `<small style="color: #ffd93d; font-weight: bold;">Expires in ${hoursUntilExpiry}h</small>`;
        }

        return `
                <div class="activity-item" ${isExpired ? 'style="opacity: 0.7; background: rgba(255, 107, 107, 0.05);"' : ""}>
                    <div class="activity-icon" ${isExpired ? 'style="background: rgba(255, 107, 107, 0.2); color: #ff6b6b;"' : ""}>
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${titleText}</div>
                        <div class="activity-details">${key.key_code || key.key}</div>
                        <div class="activity-time">
                            ${new Date(key.created_at).toLocaleString()}
                            ${statusIndicator ? "<br>" + statusIndicator : ""}
                        </div>
                    </div>
                </div>
            `;
      })
      .join("");
  }

  async loadKeys() {
    const tbody = document.getElementById("keys-tbody");
    if (!tbody) return;

    try {
      tbody.innerHTML =
        '<tr><td colspan="6" class="loading">Loading keys...</td></tr>';

      const token = localStorage.getItem("admin_token");
      const response = await fetch(`${this.apiBase}/keys`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to load keys");
      }

      const data = await response.json();
      const now = new Date();

      tbody.innerHTML = data.keys
        .map((key) => {
          const expiresAt = new Date(key.expires_at);
          const isExpired = expiresAt < now;
          const timeUntilExpiry = expiresAt - now;
          const hoursUntilExpiry = Math.floor(
            timeUntilExpiry / (1000 * 60 * 60),
          );

          // Determine status based on actual expiration time
          let actualStatus = isExpired ? "expired" : "active";
          let statusClass = "active";
          let statusText = "Active";

          // Check actual status from database
          if (key.status === "revoked") {
            statusClass = "revoked";
            statusText = "REVOKED";
          } else if (isExpired) {
            statusClass = "expired";
            statusText = "EXPIRED";
          } else if (hoursUntilExpiry <= 24) {
            statusClass = "warning";
            statusText = `Expires in ${hoursUntilExpiry}h`;
          }

          // Format expiration date with warning colors
          let expiryClass = "";
          if (isExpired) {
            expiryClass = 'style="color: #ff6b6b; font-weight: 600;"';
          } else if (hoursUntilExpiry <= 24) {
            expiryClass = 'style="color: #ffd93d; font-weight: 600;"';
          }

          return `
                    <tr ${isExpired ? 'style="opacity: 0.7; background: rgba(255, 107, 107, 0.05);"' : ""}>
                        <td>
                            <div class="key-cell">
                                <span class="key-value" ${isExpired ? 'style="opacity: 0.6;"' : ""}>${key.key_code || key.key}</span>
                                <div class="key-actions">
                                    <button class="action-btn copy" onclick="adminDashboard.copyKeyToClipboard('${key.key_code || key.key}')" title="Copy Key">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                        <td ${isExpired ? 'style="opacity: 0.6;"' : ""}>${key.hwid || "Not bound"}</td>
                        <td ${isExpired ? 'style="opacity: 0.6;"' : ""}>${new Date(key.created_at).toLocaleString()}</td>
                        <td ${expiryClass}>
                            ${new Date(key.expires_at).toLocaleString()}
                            ${isExpired ? '<br><small style="color: #ff6b6b;">⚠ EXPIRED</small>' : ""}
                        </td>
                        <td>
                            <span class="status-badge ${statusClass}">
                                ${statusText}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-icon danger" onclick="adminDashboard.revokeKey('${key.key_code || key.key}')" title="Revoke Key">
                                    <i class="fas fa-ban"></i>
                                </button>
                                <button class="btn-icon ${isExpired ? "warning" : "info"}" onclick="adminDashboard.extendKey('${key.key_code || key.key}')" title="${isExpired ? "Reactivate Expired Key" : "Extend Key"}">
                                    <i class="fas fa-${isExpired ? "refresh" : "clock"}"></i>
                                </button>
                                <button class="btn-icon info" onclick="adminDashboard.resetHWID('${key.key_code || key.key}')" title="Reset HWID" ${!key.hwid || isExpired ? "disabled" : ""}>
                                    <i class="fas fa-sync"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
        })
        .join("");
    } catch (error) {
      console.error("Failed to load keys:", error);
      tbody.innerHTML =
        '<tr><td colspan="6" class="error">Failed to load keys</td></tr>';
    }
  }

  async loadUsers() {
    const tbody = document.getElementById("users-tbody");
    if (!tbody) return;

    try {
      tbody.innerHTML =
        '<tr><td colspan="6" class="loading">Loading users...</td></tr>';

      // Load unique users from keys table
      const token = localStorage.getItem("admin_token");
      const response = await fetch(`${this.apiBase}/keys?status=all`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to load users");
      }

      const data = await response.json();

      // Group by HWID to get unique users
      const users = {};
      data.keys.forEach((key) => {
        if (key.hwid) {
          if (!users[key.hwid]) {
            users[key.hwid] = {
              hwid: key.hwid,
              ip_hash: key.ip_hash || "N/A",
              keys_generated: 0,
              last_used: key.last_used_at || key.updated_at || key.created_at,
            };
          }
          users[key.hwid].keys_generated++;
          if (
            key.last_used_at &&
            key.last_used_at > users[key.hwid].last_used
          ) {
            users[key.hwid].last_used = key.last_used_at;
          }
        }
      });

      const userArray = Object.values(users);

      tbody.innerHTML = userArray
        .map(
          (user) => `
                <tr>
                    <td>${user.hwid}</td>
                    <td>${user.ip_hash}</td>
                    <td>${user.keys_generated}</td>
                    <td>${new Date(user.last_used).toLocaleString()}</td>
                    <td>
                        <span class="status-badge active">Active</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon warning" onclick="adminDashboard.resetUserHWID('${user.hwid}')" title="Reset All Keys for HWID">
                                <i class="fas fa-sync"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `,
        )
        .join("");
    } catch (error) {
      console.error("Failed to load users:", error);
      tbody.innerHTML =
        '<tr><td colspan="6" class="error">Failed to load users</td></tr>';
    }
  }

  async loadLogs() {
    const container = document.getElementById("logs-container");
    if (!container) return;

    try {
      container.innerHTML = '<div class="loading">Loading logs...</div>';

      // Show system info since we don't have a logs endpoint yet
      container.innerHTML = `
                <div class="log-entry info">
                    <div class="log-time">${new Date().toLocaleString()}</div>
                    <div class="log-message">Admin dashboard accessed by ${this.currentUser.username}</div>
                    <div class="log-level">INFO</div>
                </div>
                <div class="log-entry info">
                    <div class="log-time">${new Date().toLocaleString()}</div>
                    <div class="log-message">System monitoring active</div>
                    <div class="log-level">INFO</div>
                </div>
            `;
    } catch (error) {
      console.error("Failed to load logs:", error);
      container.innerHTML = '<div class="error">Failed to load logs</div>';
    }
  }

  async loadAnalytics(period = "24h") {
    console.log("Loading analytics for period:", period);

    try {
      const response = await fetch(
        `${this.apiBase}/analytics?period=${period}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
        },
      );

      if (response.ok) {
        const data = await response.json();
        this.updateAnalyticsCharts(data);
        this.showSuccess("Analytics loaded successfully");
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to load analytics");
      }
    } catch (error) {
      console.error("Failed to load analytics:", error);
      this.showError("Failed to load analytics: " + error.message);
    }
  }

  updateAnalyticsCharts(data) {
    try {
      console.log("Updating analytics charts with data:", data);
      this.renderCSSChart(
        "keys-chart",
        data.keyGeneration || [],
        "line",
        "Keys Generated",
      );
      this.renderCSSChart(
        "activity-chart",
        data.userActivity || [],
        "bar",
        "User Sessions",
      );
      this.renderAnalyticsSummary(data.summary || {});
      console.log("Analytics charts updated successfully");
    } catch (error) {
      console.error("Failed to update analytics charts:", error);
      this.displayAnalyticsFallback(data);
    }
  }

  renderCSSChart(containerId, data, type, title) {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error("Chart container not found:", containerId);
      return;
    }

    if (!data || !data.length) {
      this.renderNoDataChart(containerId, title);
      return;
    }

    console.log(`Rendering ${type} chart for ${containerId} with data:`, data);

    const maxValue = Math.max(...data.map((item) => item.count));
    const minValue = 0;
    const range = maxValue || 1;

    let chartHTML = "";

    if (type === "line") {
      chartHTML = `
                <div class="chart-line">
                    <div class="chart-line-path">
                        ${data
                          .map((item, index) => {
                            const height = Math.max(
                              5,
                              (item.count / range) * 130,
                            );
                            return `<div class="chart-point" style="bottom: ${height}px" title="${item.count} on ${new Date(item.date).toLocaleDateString()}"></div>`;
                          })
                          .join("")}
                    </div>
                </div>
            `;
    } else if (type === "bar") {
      chartHTML = `
                <div class="chart-bars">
                    ${data
                      .map((item) => {
                        const height = Math.max(10, (item.count / range) * 130);
                        return `
                            <div class="chart-bar" style="height: ${height}px" title="${item.count} on ${new Date(item.date).toLocaleDateString()}">
                                <div class="chart-bar-value">${item.count}</div>
                            </div>
                        `;
                      })
                      .join("")}
                </div>
            `;
    }

    const labels = data.map((item) =>
      new Date(item.date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
    );

    container.innerHTML = `
            ${chartHTML}
            <div class="chart-labels">
                ${labels.map((label) => `<span>${label}</span>`).join("")}
            </div>
        `;
  }

  renderNoDataChart(containerId, title) {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = `
                <div class="chart-no-data">
                    <span>No data available for ${title}</span>
                </div>
            `;
    }
  }

  renderAnalyticsSummary(summary) {
    const summaryHTML = `
            <div class="chart-summary">
                <div class="chart-summary-item">
                    <span>Total Keys:</span>
                    <span class="chart-summary-value">${summary.totalKeys || 0}</span>
                </div>
                <div class="chart-summary-item">
                    <span>Active Sessions:</span>
                    <span class="chart-summary-value">${summary.activeSessions || 0}</span>
                </div>
                <div class="chart-summary-item">
                    <span>Completed Sessions:</span>
                    <span class="chart-summary-value">${summary.completedSessions || 0}</span>
                </div>
            </div>
        `;

    const keysChart = document.getElementById("keys-chart");
    const activityChart = document.getElementById("activity-chart");

    if (keysChart && !keysChart.querySelector(".chart-summary")) {
      keysChart.insertAdjacentHTML("beforeend", summaryHTML);
    }

    if (activityChart && !activityChart.querySelector(".chart-summary")) {
      activityChart.insertAdjacentHTML("beforeend", summaryHTML);
    }
  }

  displayAnalyticsFallback(data) {
    this.renderNoDataChart("keys-chart", "Key Generation");
    this.renderNoDataChart("activity-chart", "User Activity");
    this.renderAnalyticsSummary(data.summary || {});
    this.showWarning("Analytics data could not be loaded properly");
  }

  switchSection(section) {
    this.currentSection = section;

    // Close mobile menu when switching sections
    if (this.isMobile && this.sidebarOpen) {
      this.closeMobileMenu();
    }

    document.querySelectorAll(".sidebar-menu li").forEach((item) => {
      item.classList.toggle("active", item.dataset.section === section);
    });

    document.querySelectorAll(".content-section").forEach((section) => {
      section.classList.remove("active");
    });
    const targetSection = document.getElementById(`${section}-section`);
    if (targetSection) {
      targetSection.classList.add("active");
    }

    // Page titles and subtitles
    const titles = {
      dashboard: "Dashboard",
      keys: "Key Management",
      users: "User Management",
      analytics: "Analytics",
      scripts: "Script Manager",
      logs: "System Logs",
      settings: "Settings",
    };
    const subtitles = {
      dashboard: "System Overview",
      keys: "Manage License Keys",
      users: "Manage Users",
      analytics: "System Analytics",
      scripts: "Kill Switch & Script Control",
      logs: "View System Logs",
      settings: "Configure System",
    };

    const pageTitleEl = document.getElementById("page-title");
    const pageSubtitleEl = document.getElementById("page-subtitle");

    if (pageTitleEl) pageTitleEl.textContent = titles[section] || section;
    if (pageSubtitleEl) pageSubtitleEl.textContent = subtitles[section] || "";

    // Load section-specific data
    switch (section) {
      case "dashboard":
        this.loadDashboardData();
        break;
      case "keys":
        this.loadKeys();
        break;
      case "users":
        this.loadUsers();
        break;
      case "scripts":
        this.loadScripts();
        break;
      case "logs":
        this.loadLogs();
        break;
      case "analytics":
        this.loadAnalytics();
        break;
    }
  }

  async revokeKey(key) {
    if (!confirm("Are you sure you want to revoke this key?")) return;

    try {
      const response = await fetch(`${this.apiBase}/keys/update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
        },
        body: JSON.stringify({
          key,
          action: "revoke",
        }),
      });

      if (response.ok) {
        this.showSuccess("Key revoked successfully");
        await this.loadKeys();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to revoke key");
      }
    } catch (error) {
      console.error("Failed to revoke key:", error);
      this.showError("Failed to revoke key: " + error.message);
    }
  }

  async extendKey(key) {
    const hours = prompt("Enter number of hours to extend:", "24");
    if (!hours || isNaN(hours)) return;

    try {
      const response = await fetch(`${this.apiBase}/keys/update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
        },
        body: JSON.stringify({
          key,
          action: "extend",
          hours: parseInt(hours),
        }),
      });

      if (response.ok) {
        this.showSuccess(`Key extended by ${hours} hours`);
        await this.loadKeys();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to extend key");
      }
    } catch (error) {
      console.error("Failed to extend key:", error);
      this.showError("Failed to extend key: " + error.message);
    }
  }

  async resetHWID(key) {
    if (!confirm("Are you sure you want to reset this key's HWID?")) return;

    try {
      const response = await fetch(`${this.apiBase}/keys/update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
        },
        body: JSON.stringify({
          key,
          action: "reset-hwid",
        }),
      });

      if (response.ok) {
        this.showSuccess("HWID reset successfully");
        await this.loadKeys();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to reset HWID");
      }
    } catch (error) {
      console.error("Failed to reset HWID:", error);
      this.showError("Failed to reset HWID: " + error.message);
    }
  }

  async resetUserHWID(hwid) {
    if (!confirm("Are you sure you want to reset all keys for this HWID?"))
      return;

    try {
      this.showInfo("User HWID reset functionality not yet implemented");
    } catch (error) {
      console.error("Failed to reset user HWID:", error);
      this.showError("Failed to reset user HWID: " + error.message);
    }
  }

  async cleanupExpiredKeys() {
    if (
      !confirm(
        "Are you sure you want to clean up expired keys? This will permanently delete all expired keys.",
      )
    )
      return;

    try {
      const token = localStorage.getItem("admin_token");
      const response = await fetch(`${this.apiBase}/keys`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const now = new Date();
        const expiredKeys = data.keys.filter(
          (key) => new Date(key.expires_at) < now,
        );

        if (expiredKeys.length === 0) {
          this.showInfo("No expired keys found to cleanup");
          return;
        }

        const confirmCleanup = confirm(
          `Found ${expiredKeys.length} expired keys. Continue with cleanup?`,
        );
        if (!confirmCleanup) return;

        // Clean up expired keys one by one
        let cleanedCount = 0;
        for (const key of expiredKeys) {
          try {
            const deleteResponse = await fetch(
              `${this.apiBase}/keys/${key.key_code || key.key}/delete`,
              {
                method: "DELETE",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                },
              },
            );

            if (deleteResponse.ok) {
              cleanedCount++;
            }
          } catch (error) {
            console.error(
              `Failed to delete key ${key.key_code || key.key}:`,
              error,
            );
          }
        }

        this.showSuccess(
          `Successfully cleaned up ${cleanedCount} expired keys`,
        );
        await this.loadKeys();
        await this.loadDashboardData();
      } else {
        throw new Error("Failed to load keys for cleanup");
      }
    } catch (error) {
      console.error("Failed to clean up expired keys:", error);
      this.showError("Failed to cleanup: " + error.message);
    }
  }

  async generateNewKeys() {
    const count = prompt("How many keys to generate?", "1");
    const duration = prompt("Duration in hours?", "24");

    if (!count || !duration || isNaN(count) || isNaN(duration)) return;

    try {
      const response = await fetch(`${this.apiBase}/keys/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
        },
        body: JSON.stringify({
          count: parseInt(count),
          duration: parseInt(duration),
        }),
      });

      if (response.ok) {
        const data = await response.json();
        this.showSuccess(`Generated ${data.keys.length} keys successfully`);
        await this.loadKeys();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate keys");
      }
    } catch (error) {
      console.error("Failed to generate keys:", error);
      this.showError("Failed to generate keys: " + error.message);
    }
  }

  copyKeyToClipboard(key) {
    navigator.clipboard
      .writeText(key)
      .then(() => {
        this.showSuccess("Key copied to clipboard!");
      })
      .catch(() => {
        const textArea = document.createElement("textarea");
        textArea.value = key;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        this.showSuccess("Key copied to clipboard!");
      });
  }

  initializeSettings() {
    const settings = {
      enableKeygen: true,
      keyDuration: 24,
      enableHwid: true,
      enableRatelimit: true,
    };

    if (document.getElementById("enable-keygen")) {
      document.getElementById("enable-keygen").checked = settings.enableKeygen;
    }
    if (document.getElementById("key-duration")) {
      document.getElementById("key-duration").value = settings.keyDuration;
    }
    if (document.getElementById("enable-hwid")) {
      document.getElementById("enable-hwid").checked = settings.enableHwid;
    }
    if (document.getElementById("enable-ratelimit")) {
      document.getElementById("enable-ratelimit").checked =
        settings.enableRatelimit;
    }
  }

  async saveSettings() {
    const settings = {
      enableKeygen: document.getElementById("enable-keygen")?.checked,
      keyDuration: parseInt(
        document.getElementById("key-duration")?.value || 24,
      ),
      enableHwid: document.getElementById("enable-hwid")?.checked,
      enableRatelimit: document.getElementById("enable-ratelimit")?.checked,
    };

    console.log("Saving settings:", settings);
    this.showSuccess("Settings saved successfully");
  }

  handleSearch(inputId, value) {
    switch (inputId) {
      case "key-search":
        this.searchKeys(value);
        break;
      case "user-search":
        this.searchUsers(value);
        break;
    }
  }

  async searchKeys(query) {
    if (!query.trim()) {
      this.loadKeys();
      return;
    }

    try {
      const token = localStorage.getItem("admin_token");
      const response = await fetch(
        `${this.apiBase}/keys?search=${encodeURIComponent(query)}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.ok) {
        const data = await response.json();
        this.updateKeysTable(data.keys);
      }
    } catch (error) {
      console.error("Search failed:", error);
      this.showError("Search failed");
    }
  }

  async searchUsers(query) {
    if (!query.trim()) {
      this.loadUsers();
      return;
    }

    console.log("Searching users for:", query);
  }

  updateKeysTable(keys) {
    const tbody = document.getElementById("keys-tbody");
    if (!tbody) return;

    const now = new Date();
    tbody.innerHTML = keys
      .map((key) => {
        const expiresAt = new Date(key.expires_at);
        const isExpired = expiresAt < now;
        const timeUntilExpiry = expiresAt - now;
        const hoursUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60 * 60));

        let actualStatus = isExpired ? "expired" : "active";
        let statusClass = "active";
        let statusText = "Active";

        if (key.status === "revoked") {
          statusClass = "revoked";
          statusText = "REVOKED";
        } else if (isExpired) {
          statusClass = "expired";
          statusText = "EXPIRED";
        } else if (hoursUntilExpiry <= 24) {
          statusClass = "warning";
          statusText = `Expires in ${hoursUntilExpiry}h`;
        }

        let expiryClass = "";
        if (isExpired) {
          expiryClass = 'style="color: #ff6b6b; font-weight: 600;"';
        } else if (hoursUntilExpiry <= 24) {
          expiryClass = 'style="color: #ffd93d; font-weight: 600;"';
        }

        return `
                <tr ${isExpired ? 'style="opacity: 0.7; background: rgba(255, 107, 107, 0.05);"' : ""}>
                    <td>
                        <div class="key-cell">
                            <span class="key-value" ${isExpired ? 'style="opacity: 0.6;"' : ""}>${key.key_code || key.key}</span>
                            <div class="key-actions">
                                <button class="action-btn copy" onclick="adminDashboard.copyKeyToClipboard('${key.key_code || key.key}')" title="Copy Key">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </td>
                    <td ${isExpired ? 'style="opacity: 0.6;"' : ""}>${key.hwid || "Not bound"}</td>
                    <td ${isExpired ? 'style="opacity: 0.6;"' : ""}>${new Date(key.created_at).toLocaleString()}</td>
                    <td ${expiryClass}>
                        ${new Date(key.expires_at).toLocaleString()}
                        ${isExpired ? '<br><small style="color: #ff6b6b;">⚠ EXPIRED</small>' : ""}
                    </td>
                    <td>
                        <span class="status-badge ${statusClass}">
                            ${statusText}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon danger" onclick="adminDashboard.revokeKey('${key.key_code || key.key}')" title="Revoke Key">
                                <i class="fas fa-ban"></i>
                            </button>
                            <button class="btn-icon ${isExpired ? "warning" : "info"}" onclick="adminDashboard.extendKey('${key.key_code || key.key}')" title="${isExpired ? "Reactivate Expired Key" : "Extend Key"}">
                                <i class="fas fa-${isExpired ? "refresh" : "clock"}"></i>
                            </button>
                            <button class="btn-icon info" onclick="adminDashboard.resetHWID('${key.key_code || key.key}')" title="Reset HWID" ${!key.hwid || isExpired ? "disabled" : ""}>
                                <i class="fas fa-sync"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
      })
      .join("");
  }

  startAutoRefresh() {
    setInterval(() => {
      if (this.currentSection === "dashboard") {
        this.loadDashboardData();
      }
    }, 30000);
  }

  showSuccess(message) {
    this.showNotification(message, "success");
  }

  showError(message) {
    this.showNotification(message, "error");
  }

  showWarning(message) {
    this.showNotification(message, "warning");
  }

  showInfo(message) {
    this.showNotification(message, "info");
  }

  showNotification(message, type = "info") {
    const existing = document.querySelectorAll(".admin-notification");
    existing.forEach((el) => el.remove());

    const div = document.createElement("div");
    div.className = `admin-notification notification-${type}`;

    const icons = {
      success: "fas fa-check-circle",
      error: "fas fa-exclamation-circle",
      warning: "fas fa-exclamation-triangle",
      info: "fas fa-info-circle",
    };

    div.innerHTML = `
            <div class="notification-content">
                <i class="${icons[type]}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

    document.body.appendChild(div);

    setTimeout(() => {
      if (div.parentElement) {
        div.remove();
      }
    }, 5000);

    setTimeout(() => div.classList.add("show"), 100);
  }

  async showExpiredKeys() {
    try {
      this.switchSection("keys");

      const token = localStorage.getItem("admin_token");
      const response = await fetch(`${this.apiBase}/keys?status=expired`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const now = new Date();
        const expiredKeys = data.keys.filter(
          (key) => new Date(key.expires_at) < now,
        );

        this.updateKeysTable(expiredKeys);
        this.showInfo(`Found ${expiredKeys.length} expired keys`);

        const pageTitleEl = document.getElementById("page-title");
        if (pageTitleEl) pageTitleEl.textContent = "Expired Keys";
        const pageSubtitleEl = document.getElementById("page-subtitle");
        if (pageSubtitleEl)
          pageSubtitleEl.textContent = `${expiredKeys.length} expired keys found`;
      } else {
        throw new Error("Failed to load expired keys");
      }
    } catch (error) {
      console.error("Failed to load expired keys:", error);
      this.showError("Failed to load expired keys: " + error.message);
    }
  }

  async extendKey(keyCode) {
    const hours = prompt("Extend key by how many hours?", "24");
    if (!hours || isNaN(hours)) return;

    try {
      const response = await fetch(`${this.apiBase}/keys/${keyCode}/extend`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
        },
        body: JSON.stringify({
          hours: parseInt(hours),
        }),
      });

      if (response.ok) {
        const data = await response.json();
        this.showSuccess(data.message || `Key extended by ${hours} hours`);
        await this.loadKeys();
        await this.loadDashboardData();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to extend key");
      }
    } catch (error) {
      console.error("Failed to extend key:", error);
      this.showError("Failed to extend key: " + error.message);
    }
  }

  async revokeKey(keyCode) {
    const confirm = window.confirm("Are you sure you want to revoke this key?");
    if (!confirm) return;

    try {
      const response = await fetch(`${this.apiBase}/keys/${keyCode}/revoke`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        this.showSuccess(data.message || "Key revoked successfully");
        await this.loadKeys();
        await this.loadDashboardData();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to revoke key");
      }
    } catch (error) {
      console.error("Failed to revoke key:", error);
      this.showError("Failed to revoke key: " + error.message);
    }
  }

  async resetHWID(keyCode) {
    const confirm = window.confirm(
      "Are you sure you want to reset the HWID binding for this key?",
    );
    if (!confirm) return;

    try {
      const response = await fetch(
        `${this.apiBase}/keys/${keyCode}/reset-hwid`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
        },
      );

      if (response.ok) {
        const data = await response.json();
        this.showSuccess(data.message || "HWID reset successfully");
        await this.loadKeys();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to reset HWID");
      }
    } catch (error) {
      console.error("Failed to reset HWID:", error);
      this.showError("Failed to reset HWID: " + error.message);
    }
  }

  logout() {
    localStorage.removeItem("admin_token");
    localStorage.removeItem("admin_user");
    localStorage.removeItem("admin_auth_time");
    window.location.href = "/admin/login.html";
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  async clearLogs() {
    const confirm = window.confirm("Are you sure you want to clear all logs?");
    if (!confirm) return;

    this.showInfo("Clear logs functionality not yet implemented");
  }

  setupMobileMenu() {
    // Create mobile menu button if it doesn't exist
    if (!document.querySelector(".mobile-menu-btn")) {
      const mobileBtn = document.createElement("button");
      mobileBtn.className = "mobile-menu-btn";
      mobileBtn.innerHTML = '<i class="fas fa-bars"></i>';
      mobileBtn.addEventListener("click", () => this.toggleMobileMenu());
      document.body.appendChild(mobileBtn);
    }

    // Create mobile overlay if it doesn't exist
    if (!document.querySelector(".mobile-overlay")) {
      const overlay = document.createElement("div");
      overlay.className = "mobile-overlay";
      overlay.addEventListener("click", () => this.closeMobileMenu());
      document.body.appendChild(overlay);
    }

    // Update mobile state on window resize
    this.updateMobileState();
  }

  setupResizeHandler() {
    let resizeTimeout;
    window.addEventListener("resize", () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        this.updateMobileState();
      }, 150);
    });
  }

  updateMobileState() {
    const wasMobile = this.isMobile;
    this.isMobile = window.innerWidth <= 768;

    if (wasMobile !== this.isMobile) {
      if (!this.isMobile) {
        // Switched to desktop - close mobile menu and show sidebar
        this.closeMobileMenu();
        this.showSidebar();
      } else {
        // Switched to mobile - hide sidebar by default
        this.hideSidebar();
      }
    }
  }

  toggleMobileMenu() {
    if (this.sidebarOpen) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }

  openMobileMenu() {
    if (!this.isMobile) return;

    this.sidebarOpen = true;
    const sidebar = document.querySelector(".sidebar");
    const overlay = document.querySelector(".mobile-overlay");
    const menuBtn = document.querySelector(".mobile-menu-btn");

    if (sidebar) sidebar.classList.add("mobile-open");
    if (overlay) overlay.classList.add("active");
    if (menuBtn) menuBtn.innerHTML = '<i class="fas fa-times"></i>';

    // Prevent body scroll when menu is open
    document.body.style.overflow = "hidden";
  }

  closeMobileMenu() {
    if (!this.isMobile) return;

    this.sidebarOpen = false;
    const sidebar = document.querySelector(".sidebar");
    const overlay = document.querySelector(".mobile-overlay");
    const menuBtn = document.querySelector(".mobile-menu-btn");

    if (sidebar) sidebar.classList.remove("mobile-open");
    if (overlay) overlay.classList.remove("active");
    if (menuBtn) menuBtn.innerHTML = '<i class="fas fa-bars"></i>';

    // Restore body scroll
    document.body.style.overflow = "";
  }

  showSidebar() {
    const sidebar = document.querySelector(".sidebar");
    if (sidebar) {
      sidebar.style.transform = "translateX(0)";
    }
  }

  hideSidebar() {
    const sidebar = document.querySelector(".sidebar");
    if (sidebar && this.isMobile) {
      sidebar.style.transform = "translateX(-100%)";
    }
  }

  // Script Management Methods
  async loadScripts() {
    try {
      const response = await fetch("/.netlify/functions/app/admin/scripts", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        this.scripts = data.scripts || [];
        this.updateScriptsTable();
        this.updateScriptStats();
        await this.loadSystemSettings();
      } else if (response.status === 500) {
        // Database tables might not exist yet
        console.warn("Scripts table may not exist yet");
        this.scripts = [];
        await this.loadSystemSettings();
      } else {
        throw new Error("Failed to load scripts");
      }
    } catch (error) {
      console.error("Error loading scripts:", error);
      this.scripts = [];
    }
  }

  showModal(title, content) {
    // Create a simple modal
    const modal = document.createElement("div");
    modal.style.cssText = `
      position: fixed; top: 0; left: 0; right: 0; bottom: 0;
      background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
      align-items: center; justify-content: center; padding: 20px;
    `;

    modal.innerHTML = `
      <div style="background: var(--card-background, #2d2d2d); padding: 20px; border-radius: 10px; max-width: 800px; max-height: 80vh; overflow-y: auto;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h3 style="margin: 0; color: #fff;">${title}</h3>
          <button onclick="this.closest('.modal-overlay').remove()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer;">×</button>
        </div>
        <div style="color: #fff;">${content}</div>
      </div>
    `;

    modal.className = "modal-overlay";
    document.body.appendChild(modal);
  }

  async loadSystemSettings() {
    try {
      const response = await fetch("/.netlify/functions/app/admin/system", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        this.systemSettings = {};
        if (data.settings && Array.isArray(data.settings)) {
          data.settings.forEach((setting) => {
            try {
              // Try to parse as JSON, fallback to string value
              this.systemSettings[setting.setting_key] = JSON.parse(
                setting.setting_value,
              );
            } catch (e) {
              // If JSON parse fails, use the raw value
              this.systemSettings[setting.setting_key] = setting.setting_value;
            }
          });
        }
        this.updateSystemUI();
      } else {
        // Use default settings if database not set up
        this.systemSettings = {
          global_kill_switch: false,
          maintenance_mode: false,
          maintenance_message: "System is under maintenance",
          heartbeat_interval: 30,
        };
        this.updateSystemUI();
      }
    } catch (error) {
      console.error("Error loading system settings:", error);
      // Use default settings on error
      this.systemSettings = {
        global_kill_switch: false,
        maintenance_mode: false,
        maintenance_message: "System is under maintenance",
        heartbeat_interval: 30,
      };
      this.updateSystemUI();
    }
  }

  updateSystemUI() {
    const globalKillSwitch = document.getElementById("globalKillSwitch");
    const maintenanceMode = document.getElementById("maintenanceMode");

    if (globalKillSwitch && this.systemSettings) {
      globalKillSwitch.checked =
        this.systemSettings.global_kill_switch === true;
    }
    if (maintenanceMode && this.systemSettings) {
      maintenanceMode.checked = this.systemSettings.maintenance_mode === true;
    }
  }

  updateScriptsTable() {
    // Update the grid view instead of table
    this.updateScriptsGrid();
  }

  updateScriptsGrid() {
    const scriptsGrid = document.getElementById("scriptsGrid");
    const emptyState = document.getElementById("emptyScriptsState");

    if (!scriptsGrid) return;

    // Update stats
    this.updateScriptStats();

    if (!this.scripts || this.scripts.length === 0) {
      scriptsGrid.style.display = "none";
      if (emptyState) emptyState.style.display = "block";
      return;
    }

    if (emptyState) emptyState.style.display = "none";
    scriptsGrid.style.display = "grid";

    scriptsGrid.innerHTML = this.scripts
      .map((script) => {
        const statusClass =
          script.status === "active"
            ? "status-active"
            : script.status === "disabled"
              ? "status-disabled"
              : "status-maintenance";

        const createdDate = script.created_at
          ? new Date(script.created_at).toLocaleDateString()
          : "Unknown";

        const lastHeartbeat = script.last_heartbeat
          ? new Date(script.last_heartbeat).toLocaleString()
          : "Never";

        return `
        <div class="script-card">
          <div class="script-card-header">
            <div>
              <h3 class="script-title">${script.script_name}</h3>
              <span class="script-id">${script.script_id}</span>
            </div>
            <span class="script-status ${statusClass}">${script.status}</span>
          </div>

          <div class="script-info">
            <div class="script-info-item">
              <span class="script-info-label">Version</span>
              <span class="script-info-value">${script.version || "1.0.0"}</span>
            </div>
            <div class="script-info-item">
              <span class="script-info-label">Usage Count</span>
              <span class="script-info-value">${script.usage_count || 0}</span>
            </div>
            <div class="script-info-item">
              <span class="script-info-label">Created</span>
              <span class="script-info-value">${createdDate}</span>
            </div>
            <div class="script-info-item">
              <span class="script-info-label">Last Heartbeat</span>
              <span class="script-info-value">${lastHeartbeat}</span>
            </div>
            ${
              script.description
                ? `
            <div class="script-info-item">
              <span class="script-info-label">Description</span>
              <span class="script-info-value">${script.description}</span>
            </div>
            `
                : ""
            }
          </div>

          <div class="script-actions">
            <button class="script-btn script-btn-view" onclick="adminDashboard.viewScriptDetails('${script.script_id}')" title="View Details">
              <i class="fas fa-eye"></i> View
            </button>
            <button class="script-btn script-btn-edit" onclick="adminDashboard.editScriptContent('${script.script_id}')" title="Edit Code">
              <i class="fas fa-code"></i> Edit
            </button>
            <button class="script-btn script-btn-copy" onclick="adminDashboard.copyScriptUrl('${script.script_id}')" title="Copy URL">
              <i class="fas fa-copy"></i> Copy
            </button>
            <button class="script-btn script-btn-delete" onclick="adminDashboard.deleteScript('${script.script_id}')" title="Delete Script">
              <i class="fas fa-trash"></i> Delete
            </button>
          </div>
        </div>
        `;
      })
      .join("");
  }

  updateScriptStats() {
    if (!this.scripts) {
      this.scripts = [];
    }

    const activeCount = this.scripts.filter(
      (s) => s.status === "active",
    ).length;
    const maintenanceCount = this.scripts.filter(
      (s) => s.status === "maintenance",
    ).length;
    const totalCount = this.scripts.length;
    const totalUsage = this.scripts.reduce(
      (sum, s) => sum + (s.usage_count || 0),
      0,
    );

    // Update the new stat cards
    const activeScriptsEl = document.getElementById("activeScriptsCount");
    const maintenanceScriptsEl = document.getElementById(
      "maintenanceScriptsCount",
    );
    const totalScriptsEl = document.getElementById("totalScriptsCount");
    const totalUsageEl = document.getElementById("totalScriptUsage");

    if (activeScriptsEl) activeScriptsEl.textContent = activeCount;
    if (maintenanceScriptsEl)
      maintenanceScriptsEl.textContent = maintenanceCount;
    if (totalScriptsEl) totalScriptsEl.textContent = totalCount;
    if (totalUsageEl) totalUsageEl.textContent = totalUsage.toLocaleString();
  }

  async updateScriptStatus(scriptId, status) {
    try {
      // Get the current script to preserve maintenance message if needed
      const script = this.scripts.find((s) => s.script_id === scriptId);

      // Prepare update data
      const updateData = {
        script_id: scriptId,
        status: status,
      };

      // If switching to maintenance mode, make sure we have a maintenance message
      if (status === "maintenance" && script) {
        updateData.maintenance_message =
          script.maintenance_message ||
          "This script is currently under maintenance.";
      }

      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/update",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify(updateData),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to update script");
      }

      // Show appropriate success message
      const statusMessages = {
        active: "activated",
        disabled: "disabled",
        maintenance: "put in maintenance mode",
      };
      this.showSuccess(
        `Script "${scriptId}" has been ${statusMessages[status] || status}`,
      );

      // Refresh scripts list
      await this.loadScripts();

      // Close any open modals that might be showing the script
      const openModals = document.querySelectorAll(
        '.modal[style*="display: block"]',
      );
      openModals.forEach((modal) => {
        if (modal.querySelector(".close")) {
          modal.style.display = "none";
        }
      });
    } catch (error) {
      this.showError("Failed to update script: " + error.message);
    }
  }

  editScript(scriptId) {
    const script = this.scripts.find((s) => s.script_id === scriptId);
    if (!script) return;

    // Close any open modals first
    const openModals = document.querySelectorAll(
      '.modal[style*="display: block"]',
    );
    openModals.forEach((modal) => {
      if (modal.querySelector(".close")) {
        modal.style.display = "none";
      }
    });

    document.getElementById("editScriptId").value = script.script_id;
    document.getElementById("editStatus").value = script.status;
    document.getElementById("editMaintenanceMessage").value =
      script.maintenance_message || "";
    document.getElementById("editAllowedUsers").value = script.allowed_users
      ? script.allowed_users.join(", ")
      : "";

    document.getElementById("editScriptModal").style.display = "block";
  }

  async toggleGlobalKillSwitch() {
    const enabled = document.getElementById("globalKillSwitch").checked;
    try {
      await this.updateSystemSetting("global_kill_switch", enabled);
      this.showAlert(
        `Global kill switch ${enabled ? "ENABLED" : "disabled"}`,
        enabled ? "error" : "success",
      );
    } catch (error) {
      document.getElementById("globalKillSwitch").checked = !enabled;
      this.showError(
        "Database not set up yet. Please set up the database first.",
      );
    }
  }

  async toggleMaintenanceMode() {
    const enabled = document.getElementById("maintenanceMode").checked;
    try {
      await this.updateSystemSetting("maintenance_mode", enabled);
      this.showAlert(
        `Maintenance mode ${enabled ? "ENABLED" : "disabled"}`,
        enabled ? "warning" : "success",
      );
    } catch (error) {
      document.getElementById("maintenanceMode").checked = !enabled;
      this.showError(
        "Database not set up yet. Please set up the database first.",
      );
    }
  }

  async updateSystemSetting(key, value) {
    try {
      const response = await fetch(
        "/.netlify/functions/app/admin/system/update",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            setting_key: key,
            setting_value: value,
          }),
        },
      );

      if (!response.ok) {
        if (response.status === 500) {
          throw new Error("Database tables not set up yet");
        }
        const data = await response.json();
        throw new Error(data.error || "Failed to update setting");
      }
    } catch (error) {
      if (
        error.message.includes("Database") ||
        error.message.includes("table")
      ) {
        throw new Error(
          "Database not set up yet. Please set up the database first.",
        );
      }
      throw error;
    }
  }

  closeModal(modalId) {
    document.getElementById(modalId).style.display = "none";
  }

  showAlert(message, type) {
    if (type === "error") {
      this.showError(message);
    } else if (type === "warning") {
      this.showWarning(message);
    } else {
      this.showSuccess(message);
    }
  }

  // Script upload and management methods
  async uploadScript(scriptData) {
    try {
      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/upload",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify(scriptData),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to upload script");
      }

      this.showSuccess("Script uploaded successfully! URL: " + data.script_url);
      await this.loadScripts();
      return data;
    } catch (error) {
      this.showError("Failed to upload script: " + error.message);
      throw error;
    }
  }

  async getScriptContent(scriptId) {
    try {
      const response = await fetch(
        `/.netlify/functions/app/admin/scripts/content?script_id=${scriptId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to load script content");
      }

      const data = await response.json();
      return data;
    } catch (error) {
      this.showError("Failed to load script content: " + error.message);
      throw error;
    }
  }

  async updateScriptContent(scriptId, content) {
    try {
      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/update-content",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            script_id: scriptId,
            script_content: content,
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to save script content");
      }

      this.showSuccess("Script content saved successfully");
    } catch (error) {
      this.showError("Failed to save script content: " + error.message);
      throw error;
    }
  }

  copyToClipboard(text) {
    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.showSuccess("Copied to clipboard!");
        })
        .catch(() => {
          this.fallbackCopyToClipboard(text);
        });
    } else {
      this.fallbackCopyToClipboard(text);
    }
  }

  async editScriptContent(scriptId) {
    try {
      const response = await fetch(
        `/.netlify/functions/app/admin/scripts/content?script_id=${scriptId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to load script content");
      }

      const data = await response.json();

      document.getElementById("editContentScriptId").value = scriptId;
      document.getElementById("editScriptUrl").value =
        data.script_url || `/.netlify/functions/app/scripts/${scriptId}.lua`;
      document.getElementById("editScriptCodeEditor").value =
        data.content || "-- No content available";
      document.getElementById("editScriptContentTitle").textContent =
        `Edit Script: ${scriptId}`;

      document.getElementById("editScriptContentModal").style.display = "block";
    } catch (error) {
      this.showError("Failed to load script content: " + error.message);
    }
  }

  fallbackCopyToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand("copy");
      this.showSuccess("Copied to clipboard!");
    } catch (err) {
      this.showError("Failed to copy to clipboard");
    }

    document.body.removeChild(textArea);
  }

  generateScriptUrl(scriptId) {
    return `https://projectmadara.com/scripts/${scriptId}.lua`;
  }

  generateLoadstringCode(scriptId) {
    const url = this.generateScriptUrl(scriptId);
    return `loadstring(game:HttpGet("${url}"))()`;
  }

  validateLuaCode(code) {
    const issues = [];

    if (!code.trim()) {
      issues.push("Script is empty");
      return issues;
    }

    // Check for balanced parentheses
    const openParens = (code.match(/\(/g) || []).length;
    const closeParens = (code.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      issues.push("Unmatched parentheses");
    }

    // Check for balanced brackets
    const openBrackets = (code.match(/\[/g) || []).length;
    const closeBrackets = (code.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
      issues.push("Unmatched brackets");
    }

    // Check for balanced braces
    const openBraces = (code.match(/\{/g) || []).length;
    const closeBraces = (code.match(/\}/g) || []).length;
    if (openBraces !== closeBraces) {
      issues.push("Unmatched braces");
    }

    // Check for basic Lua syntax
    if (
      !code.includes("local") &&
      !code.includes("function") &&
      !code.includes("--")
    ) {
      issues.push("No recognizable Lua syntax found");
    }

    return issues;
  }

  formatLuaCode(code) {
    // Basic Lua code formatting
    let formatted = code;

    // Replace tabs with spaces
    formatted = formatted.replace(/\t/g, "    ");

    // Remove trailing whitespace
    formatted = formatted.replace(/\s+$/gm, "");

    // Remove excessive newlines
    formatted = formatted.replace(/\n{3,}/g, "\n\n");

    // Add newlines after certain keywords
    formatted = formatted.replace(/(local\s+function\s+\w+\([^)]*\))/g, "$1\n");
    formatted = formatted.replace(/(function\s+\w+\([^)]*\))/g, "$1\n");
    formatted = formatted.replace(/(\bend\b)/g, "$1\n");

    return formatted;
  }

  getScriptMetrics() {
    if (!this.scripts) return null;

    const total = this.scripts.length;
    const active = this.scripts.filter((s) => s.status === "active").length;
    const disabled = this.scripts.filter((s) => s.status === "disabled").length;
    const maintenance = this.scripts.filter(
      (s) => s.status === "maintenance",
    ).length;
    const totalUsage = this.scripts.reduce(
      (sum, s) => sum + (s.usage_count || 0),
      0,
    );

    return {
      total,
      active,
      disabled,
      maintenance,
      totalUsage,
      activePercentage: total > 0 ? Math.round((active / total) * 100) : 0,
    };
  }

  async saveScriptContent() {
    try {
      const scriptId = document.getElementById("editContentScriptId").value;
      const content = document.getElementById("editScriptCodeEditor").value;

      if (!content.trim()) {
        this.showError("Script content cannot be empty");
        return;
      }

      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/update-content",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            script_id: scriptId,
            script_content: content,
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to save script content");
      }

      this.showSuccess("Script content saved successfully");
      document.getElementById("editScriptContentModal").style.display = "none";
    } catch (error) {
      this.showError("Failed to save script content: " + error.message);
    }
  }

  async createScript(scriptData) {
    try {
      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/create",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify(scriptData),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create script");
      }

      this.showSuccess("Script created successfully");
      await this.loadScripts();
      return data.script;
    } catch (error) {
      this.showError("Failed to create script: " + error.message);
      throw error;
    }
  }

  async deleteScript(scriptId) {
    try {
      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/delete",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            script_id: scriptId,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to delete script");
      }

      this.showSuccess("Script deleted successfully");
      await this.loadScripts();
    } catch (error) {
      this.showError("Failed to delete script: " + error.message);
      throw error;
    }
  }

  async getScriptAnalytics(scriptId) {
    try {
      const response = await fetch(
        `/.netlify/functions/app/admin/scripts/analytics?script_id=${scriptId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to get script analytics");
      }

      const data = await response.json();
      return data.analytics;
    } catch (error) {
      console.error("Error getting script analytics:", error);
      return null;
    }
  }

  async getScriptLogs(scriptId, limit = 100) {
    try {
      const response = await fetch(
        `/.netlify/functions/app/admin/scripts/logs?script_id=${scriptId}&limit=${limit}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to get script logs");
      }

      const data = await response.json();
      return data.logs;
    } catch (error) {
      console.error("Error getting script logs:", error);
      return [];
    }
  }

  async getScriptSessions(scriptId) {
    try {
      const response = await fetch(
        `/.netlify/functions/app/admin/scripts/sessions?script_id=${scriptId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to get script sessions");
      }

      const data = await response.json();
      return data.sessions;
    } catch (error) {
      console.error("Error getting script sessions:", error);
      return [];
    }
  }

  async terminateSession(sessionToken) {
    try {
      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/terminate-session",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            session_token: sessionToken,
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to terminate session");
      }

      this.showSuccess("Session terminated successfully");
    } catch (error) {
      this.showError("Failed to terminate session: " + error.message);
      throw error;
    }
  }

  async bulkScriptOperation(action, scriptIds) {
    try {
      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/bulk",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            action: action,
            script_ids: scriptIds,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to execute bulk operation");
      }

      this.showSuccess(`Bulk ${action} operation completed successfully`);
      await this.loadScripts();
      return data.results;
    } catch (error) {
      this.showError("Failed to execute bulk operation: " + error.message);
      throw error;
    }
  }

  exportScripts() {
    if (!this.scripts || this.scripts.length === 0) {
      this.showWarning("No scripts to export");
      return;
    }

    const scriptsData = this.scripts.map((script) => ({
      name: script.script_name,
      id: script.script_id,
      status: script.status,
      version: script.version,
      description: script.description,
      usage_count: script.usage_count,
      created_at: script.created_at,
      updated_at: script.updated_at,
    }));

    const dataStr = JSON.stringify(scriptsData, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `scripts_export_${new Date().toISOString().split("T")[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    this.showSuccess("Scripts exported successfully");
  }

  async enableAllScripts() {
    if (!this.scripts || this.scripts.length === 0) {
      this.showWarning("No scripts found");
      return;
    }

    const scriptIds = this.scripts
      .filter((script) => script.status !== "active")
      .map((script) => script.script_id);

    if (scriptIds.length === 0) {
      this.showInfo("All scripts are already enabled");
      return;
    }

    try {
      await this.bulkScriptOperation("enable", scriptIds);
    } catch (error) {
      // Error already handled in bulkScriptOperation
    }
  }

  async disableAllScripts() {
    if (!this.scripts || this.scripts.length === 0) {
      this.showWarning("No scripts found");
      return;
    }

    const scriptIds = this.scripts
      .filter((script) => script.status !== "disabled")
      .map((script) => script.script_id);

    if (scriptIds.length === 0) {
      this.showInfo("All scripts are already disabled");
      return;
    }

    try {
      await this.bulkScriptOperation("disable", scriptIds);
    } catch (error) {
      // Error already handled in bulkScriptOperation
    }
  }

  async maintenanceAllScripts() {
    if (!this.scripts || this.scripts.length === 0) {
      this.showWarning("No scripts found");
      return;
    }

    const scriptIds = this.scripts
      .filter((script) => script.status !== "maintenance")
      .map((script) => script.script_id);

    if (scriptIds.length === 0) {
      this.showInfo("All scripts are already in maintenance mode");
      return;
    }

    try {
      await this.bulkScriptOperation("maintenance", scriptIds);
    } catch (error) {
      // Error already handled in bulkScriptOperation
    }
  }

  showScriptDetails(scriptId) {
    const script = this.scripts.find((s) => s.script_id === scriptId);
    if (!script) {
      this.showError("Script not found");
      return;
    }

    // Store current script ID for refresh functionality
    this.currentDetailScript = scriptId;

    // Load and display script details
    this.loadScriptDetails(scriptId);
  }

  async loadScriptDetails(scriptId) {
    try {
      // This method would populate the script details modal
      // Implementation depends on the modal structure
      const script = this.scripts.find((s) => s.script_id === scriptId);
      if (!script) return;

      const analytics = await this.getScriptAnalytics(scriptId);
      const logs = await this.getScriptLogs(scriptId, 10);
      const sessions = await this.getScriptSessions(scriptId);

      // Populate modal with data - this would interact with DOM elements
      console.log("Script details loaded:", {
        script,
        analytics,
        logs,
        sessions,
      });
    } catch (error) {
      this.showError("Failed to load script details: " + error.message);
    }
  }

  showBulkModal() {
    // Populate script checkboxes
    const checkboxContainer = document.getElementById("scriptCheckboxes");
    if (checkboxContainer && this.scripts) {
      checkboxContainer.innerHTML = this.scripts
        .map(
          (script) => `
        <div style="margin-bottom: 8px;">
          <label style="display: flex; align-items: center; cursor: pointer;">
            <input type="checkbox" name="selectedScripts" value="${script.script_id}" style="margin-right: 8px;">
            <span>${script.script_name} (${script.script_id})</span>
            <span style="margin-left: auto; color: ${script.status === "active" ? "green" : "orange"}; font-size: 12px;">
              ${script.status}
            </span>
          </label>
        </div>
      `,
        )
        .join("");
    }

    document.getElementById("bulkModal").style.display = "block";
  }

  async executeBulkOperation() {
    try {
      const operation = document.getElementById("bulkAction").value;
      const scriptIds = Array.from(
        document.querySelectorAll('input[name="selectedScripts"]:checked'),
      ).map((checkbox) => checkbox.value);

      if (scriptIds.length === 0) {
        this.showWarning("Please select at least one script");
        return;
      }

      await this.bulkScriptOperation(operation, scriptIds);
      document.getElementById("bulkModal").style.display = "none";
    } catch (error) {
      this.showError("Failed to execute bulk operation: " + error.message);
    }
  }

  async addScript() {
    try {
      const name = document.getElementById("addScriptName").value.trim();
      const id = document.getElementById("addScriptId").value.trim();
      const description = document
        .getElementById("addScriptDescription")
        .value.trim();
      const version = document.getElementById("addScriptVersion").value.trim();

      if (!name || !id) {
        this.showError("Script name and ID are required");
        return;
      }

      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/create",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            script_name: name,
            script_id: id,
            description: description || "",
            version: version || "1.0.0",
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create script");
      }

      this.showSuccess("Script created successfully!");
      await this.loadScripts();
      document.getElementById("addScriptModal").style.display = "none";
      document.getElementById("addScriptForm").reset();
    } catch (error) {
      this.showError("Failed to create script: " + error.message);
    }
  }

  async updateScript() {
    try {
      const scriptId = document.getElementById("editScriptId").value;
      const status = document.getElementById("editStatus").value;
      const maintenance_message = document.getElementById(
        "editMaintenanceMessage",
      ).value;

      // Parse allowed users
      const allowedUsersString =
        document.getElementById("editAllowedUsers").value;
      const allowed_users = allowedUsersString
        ? allowedUsersString
            .split(",")
            .map((user) => user.trim())
            .filter((user) => user.length > 0)
        : [];

      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/update",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            script_id: scriptId,
            status: status,
            maintenance_message: maintenance_message,
            allowed_users: allowed_users,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update script");
      }

      // Show status-specific message
      const statusMessages = {
        active: "activated",
        disabled: "disabled",
        maintenance: "put in maintenance mode",
      };
      this.showSuccess(
        `Script "${scriptId}" has been ${statusMessages[status] || "updated"} successfully!`,
      );

      await this.loadScripts();
      document.getElementById("editScriptModal").style.display = "none";
    } catch (error) {
      this.showError("Failed to update script: " + error.message);
    }
  }

  async saveSystemSettings() {
    try {
      const globalKillSwitch =
        document.getElementById("globalKillSwitch").checked;
      const maintenanceMode =
        document.getElementById("maintenanceMode").checked;

      const settings = [
        { key: "global_kill_switch", value: globalKillSwitch },
        { key: "maintenance_mode", value: maintenanceMode },
      ];

      for (const setting of settings) {
        const response = await fetch("/.netlify/functions/app/admin/system", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            setting_key: setting.key,
            setting_value: setting.value,
          }),
        });

        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.error || "Failed to save settings");
        }
      }

      this.showSuccess("System settings saved successfully!");
      document.getElementById("systemSettingsModal").style.display = "none";
    } catch (error) {
      this.showError("Failed to save system settings: " + error.message);
    }
  }

  copyScriptUrl(scriptId) {
    try {
      let scriptUrl;

      if (scriptId) {
        // Generate URL for specific script
        scriptUrl = `${window.location.origin}/.netlify/functions/app/scripts/${scriptId}.lua`;
      } else {
        // Fallback to modal input if no scriptId provided
        const urlInput = document.getElementById("editScriptUrl");
        if (urlInput && urlInput.value) {
          scriptUrl = urlInput.value.startsWith("http")
            ? urlInput.value
            : `${window.location.origin}${urlInput.value}`;
        } else {
          this.showError("No script URL available to copy");
          return;
        }
      }

      // Modern clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard
          .writeText(scriptUrl)
          .then(() => {
            this.showSuccess("✅ Script URL copied to clipboard!");
          })
          .catch(() => {
            this.fallbackCopyToClipboard(scriptUrl);
          });
      } else {
        this.fallbackCopyToClipboard(scriptUrl);
      }

      // Also show the URL in a modal for easy access
      const loadstringCode = `loadstring(game:HttpGet("${scriptUrl}"))()`;

      setTimeout(() => {
        if (
          confirm(
            `Script URL copied!\n\nLoadstring code:\n${loadstringCode}\n\nWould you like to copy the loadstring code instead?`,
          )
        ) {
          if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(loadstringCode);
          } else {
            this.fallbackCopyToClipboard(loadstringCode);
          }
          this.showSuccess("✅ Loadstring code copied to clipboard!");
        }
      }, 1000);
    } catch (error) {
      this.showError("❌ Failed to copy script URL: " + error.message);
    }
  }

  fallbackCopyToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand("copy");
      this.showSuccess("✅ Script URL copied to clipboard!");
    } catch (err) {
      this.showError("❌ Failed to copy to clipboard");
    }

    document.body.removeChild(textArea);
  }

  formatLuaCode() {
    const codeEditor = document.getElementById("editScriptCodeEditor");
    if (codeEditor) {
      // Basic Lua formatting - add proper indentation
      let code = codeEditor.value;
      const lines = code.split("\n");
      let indentLevel = 0;
      const formatted = lines
        .map((line) => {
          const trimmed = line.trim();
          if (trimmed.includes("end") || trimmed.includes("}")) {
            indentLevel = Math.max(0, indentLevel - 1);
          }
          const formatted = "  ".repeat(indentLevel) + trimmed;
          if (
            trimmed.includes("function") ||
            trimmed.includes("if") ||
            trimmed.includes("for") ||
            trimmed.includes("while") ||
            trimmed.includes("{")
          ) {
            indentLevel++;
          }
          return formatted;
        })
        .join("\n");

      codeEditor.value = formatted;
      this.showInfo("Code formatted successfully!");
    }
  }

  validateLuaCode() {
    const codeEditor = document.getElementById("editScriptCodeEditor");
    if (codeEditor) {
      const code = codeEditor.value;

      // Basic Lua syntax validation
      const issues = [];

      if (!code.trim()) {
        issues.push("Code is empty");
      }

      // Check for basic Lua syntax issues
      const lines = code.split("\n");
      lines.forEach((line, index) => {
        const trimmed = line.trim();
        if (
          trimmed.includes("function") &&
          !trimmed.includes("end") &&
          !code.includes("end")
        ) {
          issues.push(`Line ${index + 1}: Function without 'end'`);
        }
      });

      if (issues.length === 0) {
        this.showSuccess("✅ Code validation passed!");
      } else {
        this.showWarning("⚠️ Validation issues found:\n" + issues.join("\n"));
      }
    }
  }

  // Add filter functionality for the new design
  filterScripts() {
    const searchInput = document.getElementById("scriptSearchInput");
    const statusFilter = document.getElementById("statusFilter");

    if (!searchInput || !statusFilter) return;

    const searchTerm = searchInput.value.toLowerCase().trim();
    const statusValue = statusFilter.value;

    let filteredScripts = this.scripts || [];

    // Filter by search term
    if (searchTerm) {
      filteredScripts = filteredScripts.filter(
        (script) =>
          (script.script_name &&
            script.script_name.toLowerCase().includes(searchTerm)) ||
          (script.script_id &&
            script.script_id.toLowerCase().includes(searchTerm)) ||
          (script.description &&
            script.description.toLowerCase().includes(searchTerm)),
      );
    }

    // Filter by status
    if (statusValue !== "all") {
      filteredScripts = filteredScripts.filter(
        (script) => script.status === statusValue,
      );
    }

    // Temporarily store original scripts and show filtered
    const originalScripts = this.scripts;
    this.scripts = filteredScripts;
    this.updateScriptsTable();
    this.scripts = originalScripts;

    // Show filter results
    if (filteredScripts.length === 0) {
      this.showInfo(`No scripts found matching your criteria`);
    } else if (filteredScripts.length !== originalScripts.length) {
      this.showInfo(
        `Showing ${filteredScripts.length} of ${originalScripts.length} scripts`,
      );
    }
  }

  async deleteScript(scriptId) {
    if (
      !confirm(
        `Are you sure you want to delete script "${scriptId}"?\n\nThis action cannot be undone.`,
      )
    ) {
      return;
    }

    try {
      const response = await fetch(
        "/.netlify/functions/app/admin/scripts/delete",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("admin_token")}`,
          },
          body: JSON.stringify({
            script_id: scriptId,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to delete script");
      }

      this.showSuccess(`✅ Script "${scriptId}" deleted successfully!`);
      await this.loadScripts();
    } catch (error) {
      this.showError("❌ Failed to delete script: " + error.message);
    }
  }

  async viewScriptDetails(scriptId) {
    try {
      const script = this.scripts.find((s) => s.script_id === scriptId);
      if (!script) {
        this.showError("Script not found");
        return;
      }

      // Close any open modals first
      const openModals = document.querySelectorAll(
        '.modal[style*="display: block"]',
      );
      openModals.forEach((modal) => {
        if (modal.querySelector(".close")) {
          modal.style.display = "none";
        }
      });

      // Create a detailed view modal
      const modal = document.createElement("div");
      modal.className = "modal";
      modal.style.display = "block";
      modal.innerHTML = `
         <div class="modal-content" style="max-width: 800px;">
           <div class="modal-header">
             <h3>📊 Script Details: ${script.script_name}</h3>
             <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
           </div>
           <div class="modal-body">
             <div class="script-details-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px;">
               <div class="detail-card" style="background: var(--bg-secondary); padding: 15px; border-radius: 10px; border: 1px solid var(--border-color);">
                 <h4 style="margin: 0 0 10px 0; color: var(--text-primary);">Basic Information</h4>
                 <p><strong>ID:</strong> ${script.script_id}</p>
                 <p><strong>Version:</strong> ${script.version || "1.0.0"}</p>
                 <p><strong>Status:</strong> <span class="status-${script.status}">${script.status.toUpperCase()}</span></p>
                 <p><strong>Created:</strong> ${script.created_at ? new Date(script.created_at).toLocaleString() : "Unknown"}</p>
               </div>
               <div class="detail-card" style="background: var(--bg-secondary); padding: 15px; border-radius: 10px; border: 1px solid var(--border-color);">
                 <h4 style="margin: 0 0 10px 0; color: var(--text-primary);">Usage Statistics</h4>
                 <p><strong>Total Executions:</strong> ${script.usage_count || 0}</p>
                 <p><strong>Last Heartbeat:</strong> ${script.last_heartbeat ? new Date(script.last_heartbeat).toLocaleString() : "Never"}</p>
                 <p><strong>Kill Switch:</strong> ${script.kill_switch_enabled ? "Enabled" : "Disabled"}</p>
               </div>
             </div>
            ${
              script.description
                ? `
            <div style="background: var(--bg-tertiary); padding: 15px; border-radius: 10px; margin-bottom: 20px; border: 1px solid var(--border-color);">
              <h4 style="margin: 0 0 10px 0; color: var(--text-primary);">Description</h4>
              <p style="margin: 0; color: var(--text-secondary);">${script.description}</p>
            </div>
            `
                : ""
            }
            <div style="background: var(--bg-secondary); padding: 15px; border-radius: 10px; border: 1px solid var(--border-color);">
              <h4 style="margin: 0 0 10px 0; color: var(--text-primary);">Script URL</h4>
              <div style="display: flex; gap: 10px; align-items: center;">
               <input type="text" value="${window.location.origin}/.netlify/functions/app/scripts/${scriptId}.lua"
                       readonly style="flex: 1; padding: 8px; border: 1px solid var(--border-color); border-radius: 5px; font-family: monospace; background-color: var(--bg-tertiary); color: var(--text-primary);">
                <button class="btn btn-primary btn-sm" onclick="adminDashboard.copyScriptUrl('${scriptId}')">
                  <i class="fas fa-copy"></i> Copy
                </button>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <div style="display: flex; gap: 10px; flex-wrap: wrap; width: 100%;">
              <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">Close</button>
              <button class="btn btn-success" onclick="adminDashboard.updateScriptStatus('${scriptId}', 'active')">
                <i class="fas fa-play"></i> Set Active
              </button>
              <button class="btn btn-warning" onclick="adminDashboard.updateScriptStatus('${scriptId}', 'maintenance')">
                <i class="fas fa-tools"></i> Set Maintenance
              </button>
              <button class="btn btn-danger" onclick="adminDashboard.updateScriptStatus('${scriptId}', 'disabled')">
                <i class="fas fa-stop"></i> Disable
              </button>
              <button class="btn btn-primary" onclick="adminDashboard.editScriptContent('${scriptId}')">
                <i class="fas fa-edit"></i> Edit Script
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
    } catch (error) {
      this.showError("Failed to load script details: " + error.message);
    }
  }
}

document.addEventListener("DOMContentLoaded", () => {
  window.adminDashboard = new AdminDashboard();
});
