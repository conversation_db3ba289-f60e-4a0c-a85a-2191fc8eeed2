<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Functions Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn:hover { background-color: #0056b3; }
        .btn.success { background-color: #28a745; }
        .btn.danger { background-color: #dc3545; }
        .btn.warning { background-color: #ffc107; color: black; }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success-result { border-left: 5px solid #28a745; }
        .error-result { border-left: 5px solid #dc3545; }
        .hidden { display: none; }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .function-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .function-item {
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .function-item.available {
            background-color: #d4edda;
            color: #155724;
        }
        .function-item.missing {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Admin Dashboard Functions Test</h1>

        <!-- Login Section -->
        <div class="test-section">
            <h3>1. Admin Authentication</h3>
            <input type="text" id="username" placeholder="Username" value="admin1" style="margin-right: 10px; padding: 8px;">
            <input type="password" id="password" placeholder="Password" value="admin123" style="margin-right: 10px; padding: 8px;">
            <button class="btn" onclick="doLogin()">Login</button>
            <button class="btn danger" onclick="doLogout()">Logout</button>
            <div id="authStatus"></div>
        </div>

        <!-- Function Tests -->
        <div class="test-section">
            <h3>2. Admin Dashboard Function Tests</h3>
            <div class="function-list">
                <button class="btn" onclick="testFunction('showUploadScriptModal')">Test Upload Modal</button>
                <button class="btn" onclick="testFunction('refreshScripts')">Test Refresh Scripts</button>
                <button class="btn" onclick="testFunction('enableAllScripts')">Test Enable All</button>
                <button class="btn" onclick="testFunction('disableAllScripts')">Test Disable All</button>
                <button class="btn" onclick="testFunction('maintenanceAllScripts')">Test Maintenance All</button>
                <button class="btn" onclick="testFunction('showBulkModal')">Test Bulk Modal</button>
                <button class="btn" onclick="testFunction('exportScripts')">Test Export Scripts</button>
                <button class="btn" onclick="testFunction('showSystemSettingsModal')">Test Settings Modal</button>
            </div>
            <button class="btn success" onclick="testAllFunctions()">Test All Functions</button>
            <button class="btn warning" onclick="loadAdminDashboard()">Load Admin Dashboard</button>
        </div>

        <!-- API Tests -->
        <div class="test-section">
            <h3>3. API Endpoint Tests</h3>
            <button class="btn" onclick="testScriptUpload()">Test Script Upload</button>
            <button class="btn" onclick="testScriptList()">Test Script List</button>
            <button class="btn" onclick="testSystemSettings()">Test System Settings</button>
            <button class="btn" onclick="testBulkOperations()">Test Bulk Operations</button>
        </div>

        <!-- Results -->
        <div id="results"></div>

        <!-- Hidden iframe for loading admin dashboard -->
        <iframe id="adminFrame" src="" class="hidden"></iframe>
    </div>

    <!-- Include admin dashboard scripts -->
    <script src="/admin/scripts/admin.js"></script>

    <script>
        let testResults = [];
        let adminDashboardLoaded = false;

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('authStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        function addResult(testName, success, details) {
            testResults.push({ testName, success, details, timestamp: new Date() });
            updateResults();
        }

        function updateResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>Test Results:</h3>' +
                testResults.map(result => `
                    <div class="test-result ${result.success ? 'success-result' : 'error-result'}">
                        <strong>${result.success ? '✅' : '❌'} ${result.testName}</strong><br>
                        <small>${result.timestamp.toLocaleTimeString()}</small><br>
                        ${result.details}
                    </div>
                `).join('');
        }

        async function doLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/.netlify/functions/admin/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    localStorage.setItem('admin_token', data.token);
                    showStatus(`✅ Logged in as ${data.user.username} (${data.user.role})`, 'success');
                    addResult('Admin Login', true, `Successfully logged in as ${data.user.username}`);
                } else {
                    showStatus(`❌ Login failed: ${data.error}`, 'error');
                    addResult('Admin Login', false, `Login failed: ${data.error}`);
                }
            } catch (error) {
                showStatus(`❌ Login error: ${error.message}`, 'error');
                addResult('Admin Login', false, `Error: ${error.message}`);
            }
        }

        function doLogout() {
            localStorage.removeItem('admin_token');
            showStatus('Logged out', 'info');
            addResult('Logout', true, 'Successfully logged out');
        }

        function loadAdminDashboard() {
            const iframe = document.getElementById('adminFrame');
            iframe.src = '/admin/index.html';
            iframe.classList.remove('hidden');

            iframe.onload = function() {
                try {
                    // Try to access the iframe's window and check for functions
                    const iframeWindow = iframe.contentWindow;
                    const iframeDocument = iframe.contentDocument;

                    if (iframeWindow && iframeDocument) {
                        adminDashboardLoaded = true;
                        addResult('Load Admin Dashboard', true, 'Admin dashboard loaded successfully in iframe');

                        // Check if adminDashboard object exists
                        setTimeout(() => {
                            if (iframeWindow.adminDashboard) {
                                addResult('AdminDashboard Object', true, 'AdminDashboard instance found in iframe');
                            } else {
                                addResult('AdminDashboard Object', false, 'AdminDashboard instance not found in iframe');
                            }
                        }, 2000);
                    }
                } catch (error) {
                    addResult('Load Admin Dashboard', false, `Error accessing iframe: ${error.message}`);
                }
            };
        }

        function testFunction(functionName) {
            try {
                // Check if function exists in global scope
                if (typeof window[functionName] === 'function') {
                    addResult(`Function: ${functionName}`, true, 'Function exists in global scope');
                    return;
                }

                // Check if function exists in iframe
                const iframe = document.getElementById('adminFrame');
                if (iframe.contentWindow && typeof iframe.contentWindow[functionName] === 'function') {
                    addResult(`Function: ${functionName}`, true, 'Function exists in iframe');
                    return;
                }

                // Check if AdminDashboard exists and has the method
                if (window.adminDashboard && typeof window.adminDashboard[functionName] === 'function') {
                    addResult(`Function: ${functionName}`, true, 'Function exists as AdminDashboard method');
                    return;
                }

                // Check iframe's AdminDashboard
                if (iframe.contentWindow && iframe.contentWindow.adminDashboard &&
                    typeof iframe.contentWindow.adminDashboard[functionName] === 'function') {
                    addResult(`Function: ${functionName}`, true, 'Function exists as AdminDashboard method in iframe');
                    return;
                }


                addResult(`Function: ${functionName}`, false, 'Function not found in any scope');
            } catch (error) {
                addResult(`Function: ${functionName}`, false, `Error testing function: ${error.message}`);
            }
        }

        function testAllFunctions() {
            const functions = [
                'showUploadScriptModal', 'refreshScripts', 'enableAllScripts', 
                'disableAllScripts', 'maintenanceAllScripts', 'showBulkModal',
                'exportScripts', 'showSystemSettingsModal', 'closeModal',
                'addScript', 'updateScript', 'saveSystemSettings'
            ];

            functions.forEach(func => testFunction(func));
        }

        async function testScriptUpload() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                addResult('Script Upload Test', false, 'No admin token found - please login first');
                return;
            }

            try {
                const testScript = {
                    script_name: `Test Script ${Date.now()}`,
                    script_id: `test-${Date.now()}`,
                    script_content: 'print("Hello from test!")',
                    description: 'Test script',
                    version: '1.0.0'
                };

                const response = await fetch('/.netlify/functions/app/admin/scripts/upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(testScript)
                });

                const data = await response.json();

                if (response.ok) {
                    addResult('Script Upload Test', true, `Successfully uploaded script: ${testScript.script_id}`);
                } else {
                    addResult('Script Upload Test', false, `Upload failed: ${data.error}`);
                }
            } catch (error) {
                addResult('Script Upload Test', false, `Error: ${error.message}`);
            }
        }

        async function testScriptList() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                addResult('Script List Test', false, 'No admin token found - please login first');
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/app/admin/scripts', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                const data = await response.json();

                if (response.ok) {
                    addResult('Script List Test', true, `Found ${data.scripts?.length || 0} scripts`);
                } else {
                    addResult('Script List Test', false, `Failed to get scripts: ${data.error}`);
                }
            } catch (error) {
                addResult('Script List Test', false, `Error: ${error.message}`);
            }
        }

        async function testSystemSettings() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                addResult('System Settings Test', false, 'No admin token found - please login first');
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/app/admin/system', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                const data = await response.json();

                if (response.ok) {
                    addResult('System Settings Test', true, `System settings loaded successfully`);
                } else {
                    addResult('System Settings Test', false, `Failed to get settings: ${data.error}`);
                }
            } catch (error) {
                addResult('System Settings Test', false, `Error: ${error.message}`);
            }
        }

        async function testBulkOperations() {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                addResult('Bulk Operations Test', false, 'No admin token found - please login first');
                return;
            }

            try {
                const response = await fetch('/.netlify/functions/app/admin/scripts/bulk', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ operation: 'enable', script_ids: [] })
                });

                if (response.status === 400) {
                    addResult('Bulk Operations Test', true, 'Bulk endpoint exists and responds correctly');
                } else if (response.ok) {
                    addResult('Bulk Operations Test', true, 'Bulk operations endpoint working');
                } else {
                    const data = await response.json();
                    addResult('Bulk Operations Test', false, `Bulk endpoint error: ${data.error}`);
                }
            } catch (error) {
                addResult('Bulk Operations Test', false, `Error: ${error.message}`);
            }
        }

        // Initialize
        window.addEventListener('load', function() {
            const token = localStorage.getItem('admin_token');
            if (token) {
                showStatus('✅ Admin token found', 'success');
                addResult('Initial Check', true, 'Admin token found in localStorage');
            } else {
                showStatus('ℹ️ Please login to test admin functions', 'info');
                addResult('Initial Check', true, 'No admin token found - ready for login test');
            }
        });
    </script>
</body>
</html>