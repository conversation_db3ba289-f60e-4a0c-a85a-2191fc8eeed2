async function verifyAndRedirect() {
  if (typeof grecaptcha === "undefined") {
    alert("reCAPTCHA not loaded. Please refresh the page and try again.");
    return;
  }

  const recaptchaResponse = grecaptcha.getResponse();

  if (!recaptchaResponse) {
    alert("Please complete the captcha first!");
    return;
  }

  if (recaptchaResponse.length < 20) {
    alert(
      "Invalid reCAPTCHA response. Please try completing the captcha again.",
    );
    grecaptcha.reset();
    return;
  }

  try {
    // Clear any existing completion status to prevent bypass
    localStorage.removeItem("step2_completed");

    const selectedService =
      localStorage.getItem("selected_service") || "linkvertise";

    const response = await fetch("/.netlify/functions/app/generate-token", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ recaptchaResponse }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Server validation failed");
    }

    const { token } = await response.json();

    localStorage.setItem("progression_token", token);
    localStorage.setItem("link_visit_start", Date.now().toString());
    // Set a flag indicating this step is in progress but not completed
    localStorage.setItem("step1_pending", "true");
    // Also clear any existing completion flags to prevent state conflicts
    localStorage.removeItem("step1_completed");
    localStorage.removeItem("step2_completed");

    await fetch("/.netlify/functions/app/track-link-visit", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        token: token,
        service: selectedService,
      }),
    });

    const shortenerUrls = {
      linkvertise: "https://link-target.net/1028057/yZwSOXUkU9sF",
      shrinkme: "https://shrinkme.io/keyaccess",
    };

    const targetUrl =
      shortenerUrls[selectedService] || shortenerUrls.linkvertise;
    const trackingUrl = `${targetUrl}?ref=keysystem-step1&t=${Date.now()}`;

    // Create a completion marker with timestamp that will be verified
    localStorage.setItem("link_redirect_time", Date.now().toString());

    window.location.href = trackingUrl;
  } catch (error) {
    alert("Verification failed: " + error.message);
  }
}

function onCaptchaExpired() {}

function onCaptchaError() {
  alert("reCAPTCHA error. Please refresh the page and try again.");
}

window.onload = function () {
  console.log("Step1 page loaded - checking localStorage state:", {
    token: localStorage.getItem("progression_token"),
    pending: localStorage.getItem("step1_pending"),
    completed: localStorage.getItem("step1_completed"),
  });

  // First check if we need to validate a return from linkvertise
  const pendingStep1 = localStorage.getItem("step1_pending");
  const redirectTime = localStorage.getItem("link_redirect_time");

  if (pendingStep1 === "true" && redirectTime) {
    console.log("Checking step1 completion - pending step found");
    const timeSpent = Date.now() - parseInt(redirectTime);
    // Minimum time spent on linkvertise should be at least 15 seconds
    const minimumTimeRequired = 15000;

    if (timeSpent < minimumTimeRequired) {
      alert(
        `You need to complete the link shortener step. Minimum time required: ${Math.ceil(minimumTimeRequired / 1000)} seconds.`,
      );
      localStorage.removeItem("progression_token");
      localStorage.removeItem("step1_pending");
      localStorage.removeItem("link_redirect_time");
      window.location.href = "/";
      return;
    }

    // Check referrer - this is important for validation
    const referrer = document.referrer.toLowerCase();
    const validReferrers = [
      "linkvertise.com",
      "link-center.net",
      "link-target.net",
      "link-hub.net",
      "shrinkme.io",
    ];
    const hasValidReferrer = validReferrers.some((domain) =>
      referrer.includes(domain),
    );

    if (!hasValidReferrer) {
      alert(
        "Invalid referrer detected. Please complete the link shortener step properly.",
      );
      localStorage.removeItem("progression_token");
      localStorage.removeItem("step1_pending");
      localStorage.removeItem("link_redirect_time");
      window.location.href = "/";
      return;
    }

    // Step1 is now complete, remove the pending flag
    localStorage.removeItem("step1_pending");
    localStorage.setItem("step1_completed", "true");
    console.log("Step 1 marked as completed");
  }

  const token = localStorage.getItem("progression_token");
  const step1Completed = localStorage.getItem("step1_completed");
  const step2Completed = localStorage.getItem("step2_completed");

  // If we have a token and no pending operation, we can consider step1 complete
  if (token && !pendingStep1) {
    localStorage.setItem("step1_completed", "true");
  }

  if (token && step2Completed === "true") {
    console.log("Step 2 already completed, redirecting to finals.html");
    window.location.href = "finals.html";
    return;
  }

  if (token && (step1Completed === "true" || !pendingStep1)) {
    console.log("Step 1 already completed, redirecting to step2.html");
    window.location.href = "step2.html";
    return;
  }
};
