// Advanced Anti-Tampering and Bypass Detection System
class AntiTamperSystem {
  constructor() {
    this.originalFunctions = new Map();
    this.integrityChecks = new Map();
    this.tamperDetected = false;
    this.monitoringActive = false;
    this.securityEvents = [];
    
    this.init();
  }

  init() {
    this.setupIntegrityProtection();
    this.setupDOMProtection();
    this.setupConsoleProtection();
    this.setupDevToolsDetection();
    this.setupLocalStorageProtection();
    this.startMonitoring();
  }

  // Protect critical functions from tampering
  setupIntegrityProtection() {
    // Store original function references
    this.originalFunctions.set('fetch', window.fetch);
    this.originalFunctions.set('XMLHttpRequest', window.XMLHttpRequest);
    this.originalFunctions.set('localStorage.setItem', localStorage.setItem);
    this.originalFunctions.set('localStorage.getItem', localStorage.getItem);
    this.originalFunctions.set('localStorage.removeItem', localStorage.removeItem);
    this.originalFunctions.set('localStorage.clear', localStorage.clear);

    // Monitor for function tampering
    this.monitorFunctionIntegrity();
  }

  // Monitor critical functions for tampering
  monitorFunctionIntegrity() {
    const checkIntegrity = () => {
      // Check if fetch has been tampered with
      if (window.fetch !== this.originalFunctions.get('fetch')) {
        this.reportTampering('fetch function modified');
      }

      // Check if XMLHttpRequest has been tampered with
      if (window.XMLHttpRequest !== this.originalFunctions.get('XMLHttpRequest')) {
        this.reportTampering('XMLHttpRequest modified');
      }

      // Check localStorage methods
      if (localStorage.setItem !== this.originalFunctions.get('localStorage.setItem')) {
        this.reportTampering('localStorage.setItem modified');
      }
    };

    // Check integrity every 2 seconds
    setInterval(checkIntegrity, 2000);
  }

  // Protect DOM from manipulation
  setupDOMProtection() {
    // Monitor for suspicious DOM modifications
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // Check for injection of suspicious scripts
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.tagName === 'SCRIPT') {
                const scriptContent = node.textContent || node.innerHTML;
                if (this.isSuspiciousScript(scriptContent)) {
                  this.reportTampering('Suspicious script injection detected');
                  node.remove();
                }
              }
            }
          });
        }
        
        if (mutation.type === 'attributes') {
          // Monitor for suspicious attribute changes
          if (mutation.attributeName === 'style' && mutation.target.style.display === 'none') {
            // Check if critical elements are being hidden
            if (mutation.target.id === 'recaptcha' || mutation.target.className.includes('g-recaptcha')) {
              this.reportTampering('Attempt to hide reCAPTCHA detected');
            }
          }
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeOldValue: true
    });
  }

  // Detect suspicious script content
  isSuspiciousScript(content) {
    const suspiciousPatterns = [
      'localStorage.setItem("step',
      'localStorage.setItem("progression_token"',
      'bypass', 'skip', 'hack', 'cheat',
      'document.referrer =',
      'Object.defineProperty(document, "referrer"',
      'grecaptcha.getResponse = function',
      'fetch = function',
      'XMLHttpRequest.prototype.open'
    ];

    return suspiciousPatterns.some(pattern => 
      content.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  // Protect console and detect developer tools
  setupConsoleProtection() {
    // Detect console usage
    let devtools = false;
    const threshold = 160;

    setInterval(() => {
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devtools) {
          devtools = true;
          this.reportTampering('Developer tools opened');
        }
      } else {
        devtools = false;
      }
    }, 500);

    // Override console methods to detect usage
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;

    console.log = (...args) => {
      this.reportTampering('Console.log usage detected');
      return originalLog.apply(console, args);
    };

    console.error = (...args) => {
      this.reportTampering('Console.error usage detected');
      return originalError.apply(console, args);
    };

    console.warn = (...args) => {
      this.reportTampering('Console.warn usage detected');
      return originalWarn.apply(console, args);
    };
  }

  // Enhanced developer tools detection
  setupDevToolsDetection() {
    // Method 1: Console detection
    let devtools = {open: false, orientation: null};
    const threshold = 160;

    setInterval(() => {
      if (window.outerHeight - window.innerHeight > threshold) {
        devtools.open = true;
        devtools.orientation = 'horizontal';
        this.reportTampering('DevTools opened (horizontal)');
      } else if (window.outerWidth - window.innerWidth > threshold) {
        devtools.open = true;
        devtools.orientation = 'vertical';
        this.reportTampering('DevTools opened (vertical)');
      } else {
        devtools.open = false;
        devtools.orientation = null;
      }
    }, 500);

    // Method 2: Performance timing detection
    let start = performance.now();
    debugger;
    let end = performance.now();
    
    if (end - start > 100) {
      this.reportTampering('Debugger statement execution time anomaly');
    }

    // Method 3: toString detection
    let element = new Image();
    Object.defineProperty(element, 'id', {
      get: function() {
        this.reportTampering('DevTools element inspection detected');
        return 'devtools-detected';
      }.bind(this)
    });
    
    console.log(element);
  }

  // Protect localStorage from tampering
  setupLocalStorageProtection() {
    const originalSetItem = localStorage.setItem;
    const originalGetItem = localStorage.getItem;
    const originalRemoveItem = localStorage.removeItem;
    const originalClear = localStorage.clear;

    // Override localStorage.setItem to detect tampering
    localStorage.setItem = function(key, value) {
      // Check for bypass attempts
      if (key.includes('step') && key.includes('completed') && value === 'true') {
        antiTamper.reportTampering(`Attempt to set ${key} to ${value}`);
        return; // Block the operation
      }
      
      if (key === 'progression_token' && !antiTamper.isValidTokenFormat(value)) {
        antiTamper.reportTampering(`Invalid token format attempted: ${key}`);
        return; // Block the operation
      }

      return originalSetItem.call(this, key, value);
    };

    // Monitor localStorage access patterns
    localStorage.getItem = function(key) {
      const result = originalGetItem.call(this, key);
      
      // Log suspicious access patterns
      if (key.includes('step') || key.includes('token')) {
        antiTamper.logAccess('localStorage.getItem', key);
      }
      
      return result;
    };
  }

  // Validate token format
  isValidTokenFormat(token) {
    if (!token || typeof token !== 'string') return false;
    
    // JWT tokens should have 3 parts separated by dots
    const parts = token.split('.');
    if (parts.length !== 3) return false;
    
    try {
      // Try to decode the header and payload
      const header = JSON.parse(atob(parts[0]));
      const payload = JSON.parse(atob(parts[1]));
      
      // Check for required fields
      return header.typ === 'JWT' && payload.tokenId && payload.exp;
    } catch (e) {
      return false;
    }
  }

  // Log access patterns for analysis
  logAccess(method, key) {
    const timestamp = Date.now();
    this.securityEvents.push({
      type: 'access',
      method,
      key,
      timestamp
    });

    // Keep only last 100 events
    if (this.securityEvents.length > 100) {
      this.securityEvents.shift();
    }

    // Detect rapid access patterns (potential automation)
    const recentEvents = this.securityEvents.filter(e => 
      timestamp - e.timestamp < 1000 && e.type === 'access'
    );

    if (recentEvents.length > 10) {
      this.reportTampering('Rapid localStorage access detected (possible automation)');
    }
  }

  // Start continuous monitoring
  startMonitoring() {
    if (this.monitoringActive) return;
    
    this.monitoringActive = true;
    
    // Monitor for global variable tampering
    setInterval(() => {
      this.checkGlobalTampering();
    }, 3000);

    // Monitor for suspicious network activity
    this.monitorNetworkActivity();
  }

  // Check for global variable tampering
  checkGlobalTampering() {
    // Check for bypass flags
    if (window.bypassEnabled || window.skipVerification || window.autoComplete) {
      this.reportTampering('Bypass flags detected in global scope');
    }

    // Check for function overrides
    if (typeof window.fetch !== 'function' || 
        window.fetch.toString().includes('bypass') ||
        window.fetch.toString().includes('skip')) {
      this.reportTampering('Fetch function appears to be compromised');
    }
  }

  // Monitor network activity for suspicious patterns
  monitorNetworkActivity() {
    const originalFetch = window.fetch;
    
    window.fetch = async function(...args) {
      const url = args[0];
      
      // Check for suspicious API calls
      if (typeof url === 'string') {
        if (url.includes('bypass') || url.includes('skip') || url.includes('hack')) {
          antiTamper.reportTampering(`Suspicious API call detected: ${url}`);
          throw new Error('Blocked suspicious network request');
        }
      }
      
      return originalFetch.apply(this, args);
    };
  }

  // Report tampering attempts
  reportTampering(details) {
    if (this.tamperDetected) return; // Prevent spam
    
    this.tamperDetected = true;
    
    console.error('🚨 SECURITY VIOLATION DETECTED:', details);
    
    // Log to server
    this.reportToServer({
      type: 'tampering_detected',
      details: details,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });

    // Clear localStorage and redirect
    setTimeout(() => {
      localStorage.clear();
      alert('🚨 SECURITY VIOLATION DETECTED!\n\nTampering attempt blocked. You will be redirected to start over.');
      window.location.href = '/';
    }, 1000);
  }

  // Report security events to server
  async reportToServer(event) {
    try {
      await fetch('/.netlify/functions/app/security-event', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event)
      });
    } catch (error) {
      console.error('Failed to report security event:', error);
    }
  }
}

// Initialize anti-tamper system
const antiTamper = new AntiTamperSystem();

// Export for use in other scripts
window.antiTamper = antiTamper;
