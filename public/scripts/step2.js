let mouseInteractions = 0;
let pageLoadTime = Date.now();
let interactionInterval;
let interactionsRequired = 5;

document.addEventListener("mousemove", () => mouseInteractions++);
document.addEventListener("click", () => (mouseInteractions += 2));

// Function to extract Linkvertise token from URL
function getLinkvertiseToken() {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get("lvtoken") || urlParams.get("lv_token");

  if (token) {
    // Store token in localStorage for later verification
    localStorage.setItem("stored_lv_token", token);
    return token;
  }

  return null;
}

// Function to verify Linkvertise token with server
async function verifyLinkvertiseToken(token) {
  try {
    const response = await fetch(
      "/.netlify/functions/app/verify-linkvertise-token",
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          token: token,
          progressionToken: localStorage.getItem("progression_token"),
        }),
      },
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to verify Linkvertise token");
    }

    const result = await response.json();
    if (result.verified) {
      localStorage.setItem("lv_token_verified", "true");
      console.log("Linkvertise token verified successfully");
      return true;
    } else {
      alert(
        "Invalid Linkvertise token. Please complete the link shortener step properly.",
      );
      localStorage.clear();
      window.location.href = "/";
      return false;
    }
  } catch (error) {
    console.error("Error verifying Linkvertise token:", error);
    return false;
  }
}

async function verifyAndProceed() {
  // Check if we have a valid Linkvertise token
  const lvToken = getLinkvertiseToken();
  const hasVerifiedToken = localStorage.getItem("lv_token_verified");

  if (!lvToken && !hasVerifiedToken) {
    alert(
      "⚠️ BYPASS DETECTED!\n\nYou must complete the Linkvertise step to proceed.",
    );
    localStorage.clear();
    window.location.href = "/";
    return;
  }

  // Check if we have a valid progression token which indicates step 1 was completed
  const progressionToken = localStorage.getItem("progression_token");
  // We consider step 1 complete if we have a progression token, even if the flag isn't explicitly set
  if (!progressionToken) {
    alert("You must complete Step 1 first.");
    window.location.href = "/";
    return;
  }

  // Set step1_completed flag if it's missing but we have a progression token
  if (!localStorage.getItem("step1_completed") && progressionToken) {
    localStorage.setItem("step1_completed", "true");
  }

  // Check for sufficient user interaction
  if (mouseInteractions < interactionsRequired) {
    alert(
      `Please interact with the page. You need ${interactionsRequired - mouseInteractions} more interactions.`,
    );
    return;
  }

  // Verify reCAPTCHA
  if (typeof grecaptcha === "undefined") {
    alert("reCAPTCHA not loaded. Please refresh the page and try again.");
    return;
  }

  const recaptchaResponse = grecaptcha.getResponse();

  if (!recaptchaResponse) {
    alert("Please complete the captcha first!");
    return;
  }

  if (recaptchaResponse.length < 20) {
    alert(
      "Invalid reCAPTCHA response. Please try completing the captcha again.",
    );
    grecaptcha.reset();
    return;
  }

  try {
    const token = localStorage.getItem("progression_token");
    const linkVisitStart = localStorage.getItem("link_visit_start");

    if (!token) {
      alert("Invalid session. Please start from the beginning.");
      window.location.href = "/";
      return;
    }

    const timeSpent = linkVisitStart
      ? Date.now() - parseInt(linkVisitStart)
      : 0;

    const verificationData = {
      service: localStorage.getItem("selected_service") || "linkvertise",
      timeSpent: timeSpent,
      mouseInteractions: mouseInteractions,
      screenWidth: screen.width,
      screenHeight: screen.height,
      pageLoadTime: pageLoadTime,
      referrer: document.referrer,
      lvToken: lvToken || localStorage.getItem("stored_lv_token"),
    };

    const stepResponse = await fetch("/.netlify/functions/app/verify-step", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        token: token,
        step: 2,
        recaptchaResponse: recaptchaResponse,
        verificationData: verificationData,
      }),
    });

    if (!stepResponse.ok) {
      const errorData = await stepResponse.json();

      if (errorData.bypassDetected) {
        alert(
          "⚠️ BYPASS DETECTED!\n\n" +
            errorData.error +
            "\n\nPlease use the official link shortener to support the project.",
        );
        localStorage.clear();
        window.location.href = "/";
        return;
      }

      throw new Error(errorData.error || "Step verification failed");
    }

    localStorage.setItem("step2_pending", "true");
    localStorage.setItem("link_visit_start_step2", Date.now().toString());
    localStorage.setItem("link_redirect_time_step2", Date.now().toString());

    const secondLinkvertiseUrl = "https://link-target.net/1028057/s8dgeigX8PDj";
    const trackingUrl = `${secondLinkvertiseUrl}?ref=keysystem-step2&t=${Date.now()}`;

    // Create a unique identifier for this step to prevent replay attacks
    const stepNonce = Math.random().toString(36).substring(2, 15);
    localStorage.setItem("step2_nonce", stepNonce);

    window.location.href = trackingUrl;
  } catch (error) {
    alert("Verification failed: " + error.message);
  }
}

function checkReferrerAndBypass() {
  const referrer = document.referrer.toLowerCase();

  // Check for bypass indicators
  const bypassIndicators = [
    "bypasscity.com",
    "bypass.city",
    "linkvertise.net",
    "shrink-me.io",
    "direct-link",
    "bypass",
    "skip",
    "free",
    "unlock",
  ];

  const isBypass = bypassIndicators.some((indicator) =>
    referrer.includes(indicator),
  );

  if (isBypass) {
    alert(
      "⚠️ BYPASS DETECTED!\n\nBypass services are not allowed. Please use the official link shortener to support the project.",
    );
    localStorage.clear();
    window.location.href = "/";
    return false;
  }

  // Verify we have a progression token which indicates step 1 was started
  const progressionToken = localStorage.getItem("progression_token");
  if (!progressionToken) {
    alert("You must complete Step 1 first.");
    window.location.href = "/";
    return false;
  }

  // Check for direct navigation (missing referrer)
  const validReferrerPatterns = ["step1.html", "keysystem"];

  const hasValidReferrer = validReferrerPatterns.some((pattern) =>
    referrer.includes(pattern),
  );

  if (!hasValidReferrer && !localStorage.getItem("step2_validated")) {
    alert("Invalid navigation detected. Please follow the proper steps.");
    localStorage.clear();
    window.location.href = "/";
    return false;
  }

  return true;
}

function validateSession() {
  const token = localStorage.getItem("progression_token");
  const selectedService = localStorage.getItem("selected_service");

  if (!token) {
    localStorage.clear();
    return false;
  }

  if (!selectedService) {
    localStorage.clear();
    return false;
  }

  try {
    if (token.includes(".")) {
      const payload = JSON.parse(atob(token.split(".")[1]));
      const now = Date.now() / 1000;
      if (payload.exp && payload.exp < now) {
        console.log("Token expired, clearing localStorage");
        localStorage.clear();
        return false;
      }
    }
    return true;
  } catch (e) {
    console.log("Token validation error, clearing localStorage");
    localStorage.clear();
    return false;
  }
}

function onCaptchaExpired() {}

function onCaptchaError() {
  alert("reCAPTCHA error. Please refresh the page and try again.");
}

window.addEventListener("load", function () {
  // First check if we're returning from linkvertise for step 2
  const pendingStep2 = localStorage.getItem("step2_pending");
  const redirectTime = localStorage.getItem("link_redirect_time_step2");
  const lvToken = getLinkvertiseToken();

  // Log current localStorage state for debugging
  console.log("LocalStorage state:", {
    progression_token: localStorage.getItem("progression_token"),
    step1_completed: localStorage.getItem("step1_completed"),
    step2_pending: pendingStep2,
    lv_token_received: localStorage.getItem("lv_token_received"),
  });

  // If we have a Linkvertise token in the URL, store it and verify it
  if (lvToken) {
    console.log("Linkvertise token detected");
    localStorage.setItem("lv_token_received", "true");
    verifyLinkvertiseToken(lvToken).then((verified) => {
      if (verified && pendingStep2 === "true") {
        // Step2 is now complete if we have a valid token
        localStorage.removeItem("step2_pending");
        localStorage.setItem("step2_completed", "true");
        localStorage.setItem("step2_validated", "true");
        window.location.href = "finals.html";
      }
    });
    return;
  }

  if (pendingStep2 === "true" && redirectTime) {
    const timeSpent = Date.now() - parseInt(redirectTime);
    // Minimum time spent on linkvertise should be at least 15 seconds
    const minimumTimeRequired = 15000;

    if (timeSpent < minimumTimeRequired) {
      alert(
        `You need to complete the link shortener step. Minimum time required: ${Math.ceil(minimumTimeRequired / 1000)} seconds.`,
      );
      localStorage.removeItem("step2_pending");
      localStorage.removeItem("link_redirect_time_step2");
      return;
    }

    // Check for Linkvertise token or valid referrer
    const hasLvToken =
      lvToken || localStorage.getItem("lv_token_received") === "true";
    const referrer = document.referrer.toLowerCase();
    const validReferrers = [
      "linkvertise.com",
      "link-center.net",
      "link-target.net",
      "link-hub.net",
      "shrinkme.io",
    ];
    const hasValidReferrer = validReferrers.some((domain) =>
      referrer.includes(domain),
    );

    if (!hasLvToken && !hasValidReferrer) {
      alert(
        "Invalid verification. Please complete the link shortener step properly.",
      );
      localStorage.removeItem("step2_pending");
      localStorage.removeItem("link_redirect_time_step2");
      return;
    }

    // Step2 is now complete
    localStorage.removeItem("step2_pending");
    localStorage.setItem("step2_completed", "true");
    localStorage.setItem("step2_validated", "true");
    window.location.href = "finals.html";
    return;
  }

  // Always mark step1 as completed if we have a token (fixing potential issue)
  if (
    localStorage.getItem("progression_token") &&
    !localStorage.getItem("step1_completed")
  ) {
    localStorage.setItem("step1_completed", "true");
  }

  if (!checkReferrerAndBypass()) {
    return;
  }

  if (!validateSession()) {
    alert("Session expired or invalid. Please start from the beginning.");
    window.location.href = "/";
    return;
  }

  const step2Completed = localStorage.getItem("step2_completed");
  if (step2Completed === "true") {
    window.location.href = "finals.html";
    return;
  }

  // Start monitoring for user interactions
  interactionInterval = setInterval(() => {
    const interactionCounter = document.getElementById("interactionCounter");
    if (interactionCounter) {
      interactionCounter.textContent = `Interactions: ${mouseInteractions}/${interactionsRequired}`;
    }
  }, 500);
});
