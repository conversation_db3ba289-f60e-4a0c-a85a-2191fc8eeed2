let mouseInteractions = 0;
let pageLoadTime = Date.now();

document.addEventListener('mousemove', () => mouseInteractions++);
document.addEventListener('click', () => mouseInteractions += 2);

async function generateKey() {
  if (typeof grecaptcha === 'undefined') {
    alert('reCAPTCHA not loaded. Please refresh the page and try again.');
    return;
  }

  const recaptchaResponse = grecaptcha.getResponse();

  if (!recaptchaResponse) {
    alert('Please complete the captcha first!');
    return;
  }

  if (recaptchaResponse.length < 20) {
    alert('Invalid reCAPTCHA response. Please try completing the captcha again.');
    grecaptcha.reset();
    return;
  }

  try {
    const step2Completed = localStorage.getItem('step2_completed');
    const linkVisitStartStep2 = localStorage.getItem('link_visit_start_step2');

    if (!step2Completed) {
      alert('Please complete both link verification steps first.');
      window.location.href = '/';
      return;
    }

    const progressionToken = localStorage.getItem('progression_token');
    if (!progressionToken) {
      alert('Invalid session. Please start from the beginning.');
      window.location.href = '/';
      return;
    }

    const timeSpentStep2 = linkVisitStartStep2 ? Date.now() - parseInt(linkVisitStartStep2) : 0;

    const verificationData = {
      service: 'linkvertise',
      timeSpent: timeSpentStep2,
      mouseInteractions: mouseInteractions,
      screenWidth: screen.width,
      screenHeight: screen.height,
      pageLoadTime: pageLoadTime,
      referrer: document.referrer,
      step: 'final'
    };



    const stepResponse = await fetch('/.netlify/functions/app/verify-step', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token: progressionToken,
        step: 3,
        recaptchaResponse: recaptchaResponse,
        verificationData: verificationData
      })
    });

    if (!stepResponse.ok) {
      const errorData = await stepResponse.json();

      if (errorData.bypassDetected) {
        alert("⚠️ BYPASS DETECTED!\n\n" + errorData.error + "\n\nPlease use the official link shortener to support the project.");
        localStorage.clear();
        window.location.href = '/';
        return;
      }

      // Handle token expiration specifically
      if (errorData.error && errorData.error.includes('expired')) {
        alert("Session expired. Please start from the beginning.");
        localStorage.clear();
        window.location.href = '/';
        return;
      }

      throw new Error(errorData.error || 'Final verification failed');
    }



    const canGenerateResponse = await fetch('/.netlify/functions/app/can-generate-key');
    const canGenerateData = await canGenerateResponse.json();

    if (!canGenerateResponse.ok || !canGenerateData.canGenerate) {


      const existingKeyResponse = await fetch('/.netlify/functions/app/get-todays-key');

      if (existingKeyResponse.ok) {
        const { key, expires_at } = await existingKeyResponse.json();
        displayKey(key, expires_at);
        alert('Showing your existing key for today.');
      } else {
        alert(canGenerateData.message || 'You can only generate one key per day. Please try again tomorrow.');
      }
      return;
    }



    const response = await fetch('/.netlify/functions/app/generate-key', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token: progressionToken,
        recaptchaResponse: 'step3-verified',
        skipRecaptcha: true
      })
    });

    if (!response.ok) {
      const errorData = await response.json();


      if (typeof grecaptcha !== 'undefined') {
        grecaptcha.reset();
        document.getElementById('generateBtn').disabled = true;
      }


      if (response.status === 400 && errorData.error && errorData.error.includes('captcha')) {
        throw new Error('reCAPTCHA verification failed. This should not happen since step 3 was verified. Please refresh and try again.');
      } else if (response.status === 429) {
        throw new Error('Too many attempts. Please wait a few minutes before trying again.');
      } else {
        throw new Error(errorData.error || 'Failed to generate key');
      }
    }

    const { key, expires_at } = await response.json();


    displayKey(key, expires_at);

    localStorage.removeItem('progression_token');
    localStorage.removeItem('step2_completed');
    localStorage.removeItem('link_visit_start');
    localStorage.removeItem('link_visit_start_step2');
    localStorage.removeItem('selected_service');

  } catch (error) {
    alert("Error: " + error.message);

  }
}

function displayKey(key, expires_at) {
  const keyContainer = document.getElementById('keyContainer');
  const keyDisplay = document.getElementById('keyDisplay');
  const copyBtn = document.getElementById('copyBtn');
  const resetBtn = document.getElementById('resetBtn');

  if (keyContainer && keyDisplay && copyBtn && resetBtn) {
    keyDisplay.textContent = key;

    const expiresDate = new Date(expires_at);
    const formattedDate = expiresDate.toLocaleString();

    let expirationInfo = document.getElementById('expirationInfo');
    if (!expirationInfo) {
      expirationInfo = document.createElement('div');
      expirationInfo.id = 'expirationInfo';
      expirationInfo.className = 'expiration-info';
      keyContainer.appendChild(expirationInfo);
    }

    expirationInfo.textContent = `This key will expire on ${formattedDate}. You'll need to generate a new key tomorrow.`;

    keyContainer.style.display = 'block';
    copyBtn.style.display = 'inline-block';
    resetBtn.style.display = 'inline-block';
    resetBtn.dataset.key = key;

    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
      generateBtn.style.display = 'none';
    }
  }
}

function copyKey() {
  const keyText = document.getElementById('keyDisplay').textContent;
  navigator.clipboard.writeText(keyText)
    .then(() => {
      alert('Key copied to clipboard!');
    })
    .catch(() => {
      const textArea = document.createElement('textarea');
      textArea.value = keyText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Key copied to clipboard!');
    });
}

async function resetHWID() {
  const key = document.getElementById('resetBtn').dataset.key;

  if (!key) {
    alert('No key found to reset HWID.');
    return;
  }

  try {
    document.getElementById('resetBtn').disabled = true;
    document.getElementById('resetBtn').textContent = 'Resetting...';

    const response = await fetch('/.netlify/functions/app/reset-hwid', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ key })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'HWID reset failed');
    }

    alert('HWID reset successful! You can now use your key on a different device.');
    document.getElementById('resetBtn').textContent = 'Reset HWID';
    document.getElementById('resetBtn').disabled = false;
  } catch (error) {
    alert('HWID reset failed: ' + error.message);
    document.getElementById('resetBtn').textContent = 'Reset HWID';
    document.getElementById('resetBtn').disabled = false;

  }
}

function onCaptchaComplete() {
  const generateBtn = document.getElementById('generateBtn');
  if (generateBtn) {
    generateBtn.disabled = false;
  }
}

function onCaptchaExpired() {
  const generateBtn = document.getElementById('generateBtn');
  if (generateBtn) {
    generateBtn.disabled = true;
  }
}

function onCaptchaError() {
  const generateBtn = document.getElementById('generateBtn');
  if (generateBtn) {
    generateBtn.disabled = true;
  }
  alert('reCAPTCHA error. Please refresh the page and try again.');
}

function checkFinalsPageAccess() {
  const referrer = document.referrer.toLowerCase();

  const bypassIndicators = [
    'bypasscity.com',
    'bypass.city',
    'direct-link',
    'bypass',
    'skip',
    'free',
    'unlock'
  ];

  const isBypass = bypassIndicators.some(indicator => referrer.includes(indicator));

  if (isBypass) {
    alert('⚠️ BYPASS DETECTED!\n\nBypass services are not allowed. Please use the official link shortener to support the project.');
    localStorage.clear();
    window.location.href = '/';
    return false;
  }

  // Check if session is valid
  const progressionToken = localStorage.getItem('progression_token');
  if (!progressionToken) {
    alert('Invalid session. Please start from the beginning.');
    localStorage.clear();
    window.location.href = '/';
    return false;
  }

  // Validate token expiration
  try {
    if (progressionToken.includes('.')) {
      const payload = JSON.parse(atob(progressionToken.split('.')[1]));
      const now = Date.now() / 1000;
      if (payload.exp && payload.exp < now) {
        alert('Session expired. Please start from the beginning.');
        localStorage.clear();
        window.location.href = '/';
        return false;
      }
    }
  } catch (e) {
    alert('Invalid session. Please start from the beginning.');
    localStorage.clear();
    window.location.href = '/';
    return false;
  }

  const step2Completed = localStorage.getItem('step2_completed');
  if (!step2Completed) {
    alert('Please complete both link verification steps first.');
    localStorage.clear();
    window.location.href = '/';
    return false;
  }

  return true;
}

window.onload = function() {
  if (!checkFinalsPageAccess()) {
    return;
  }

  const keyContainer = document.getElementById('keyContainer');
  const keyDisplay = document.getElementById('keyDisplay');
  const generateBtn = document.getElementById('generateBtn');
  const copyBtn = document.getElementById('copyBtn');
  const resetBtn = document.getElementById('resetBtn');

  if (keyContainer && keyDisplay && generateBtn && copyBtn && resetBtn) {
    keyContainer.style.display = 'none';
    copyBtn.style.display = 'none';
    resetBtn.style.display = 'none';
    generateBtn.disabled = true;
  }

  const token = localStorage.getItem('progression_token');

  if (!token) {
    alert('Invalid session. Please start from the beginning.');
    window.location.href = '/';
  }
};
