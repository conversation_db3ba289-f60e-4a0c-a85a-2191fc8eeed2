const { createClient } = require("@supabase/supabase-js");
const jwt = require("jsonwebtoken");
const crypto = require("crypto");

// Admin JWT secret
const ADMIN_JWT_SECRET =
  process.env.ADMIN_JWT_SECRET || "default-admin-secret-change-in-production";

// Admin credentials from environment variables
if (!process.env.ADMIN1_USERNAME || !process.env.ADMIN1_PASSWORD) {
  throw new Error(
    "Admin credentials not configured. Please set ADMIN1_USERNAME and ADMIN1_PASSWORD environment variables.",
  );
}

const ADMIN_ACCOUNTS = {};

// Add owner if configured (highest privileges)
if (process.env.OWNER_USERNAME && process.env.OWNER_PASSWORD) {
  ADMIN_ACCOUNTS[process.env.OWNER_USERNAME] = {
    password: process.env.OWNER_PASSWORD,
    role: "owner",
  };
}

// Add first admin if configured
if (process.env.ADMIN1_USERNAME && process.env.ADMIN1_PASSWORD) {
  ADMIN_ACCOUNTS[process.env.ADMIN1_USERNAME] = {
    password: process.env.ADMIN1_PASSWORD,
    role: "admin",
  };
}

// Add third admin if configured
if (process.env.ADMIN3_USERNAME && process.env.ADMIN3_PASSWORD) {
  ADMIN_ACCOUNTS[process.env.ADMIN3_USERNAME] = {
    password: process.env.ADMIN3_PASSWORD,
    role: "admin",
  };
}

// Security headers
const getSecurityHeaders = () => ({
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Content-Type": "application/json",
});

// Verify admin token
const verifyAdminToken = (token) => {
  try {
    const decoded = jwt.verify(token, ADMIN_JWT_SECRET);

    // Check if admin account still exists
    if (!ADMIN_ACCOUNTS[decoded.username]) {
      return null;
    }

    return decoded;
  } catch (error) {
    console.error("Token verification failed:", error.message);
    return null;
  }
};

// Rate limiting store
const rateLimits = new Map();

const checkRateLimit = (ip, action) => {
  const key = `${ip}:${action}`;
  const now = Date.now();

  const limits = {
    login: { max: 5, window: 15 * 60 * 1000 }, // 5 attempts per 15 minutes
    api: { max: 100, window: 60 * 1000 }, // 100 requests per minute
  };

  const limit = limits[action] || limits.api;

  if (!rateLimits.has(key)) {
    rateLimits.set(key, { count: 1, resetTime: now + limit.window });
    return true;
  }

  const record = rateLimits.get(key);

  if (now > record.resetTime) {
    rateLimits.set(key, { count: 1, resetTime: now + limit.window });
    return true;
  }

  if (record.count >= limit.max) {
    return false;
  }

  record.count++;
  return true;
};

// Initialize Supabase client
function getSupabaseClient() {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error(
      "Supabase environment variables not configured. Please set SUPABASE_URL and SUPABASE_KEY.",
    );
  }

  try {
    return createClient(supabaseUrl, supabaseKey);
  } catch (error) {
    console.error("Failed to initialize Supabase client:", error);
    throw new Error("Failed to initialize database connection");
  }
}

// Generate key function
function generateKey() {
  const segments = [];
  for (let i = 0; i < 3; i++) {
    segments.push(crypto.randomBytes(2).toString("hex").toUpperCase());
  }
  const checksum = crypto
    .createHash("md5")
    .update(segments.join("") + "PROJECT_L_SALT")
    .digest("hex")
    .substring(0, 2)
    .toUpperCase();
  segments.push(checksum);
  return `PL-${segments.join("-")}`;
}

// Secure logging
const secureLog = (level, message, context = {}) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level,
    message,
    context: {
      ...context,
      password: context.password ? "[REDACTED]" : undefined,
      token: context.token ? "[REDACTED]" : undefined,
    },
  };
  console.log(JSON.stringify(logEntry));
};

exports.handler = async (event, context) => {
  const clientIP =
    event.headers["x-forwarded-for"] ||
    event.headers["x-real-ip"] ||
    "127.0.0.1";

  // Handle CORS
  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 200,
      headers: getSecurityHeaders(),
      body: "",
    };
  }

  const path = event.path.replace("/.netlify/functions/admin", "");

  try {
    // Public login endpoint
    if (path === "/login" && event.httpMethod === "POST") {
      // Rate limiting for login
      if (!checkRateLimit(clientIP, "login")) {
        secureLog("warn", "Login rate limit exceeded", { ip: clientIP });
        return {
          statusCode: 429,
          headers: getSecurityHeaders(),
          body: JSON.stringify({
            error: "Too many login attempts. Please try again later.",
          }),
        };
      }

      let body;
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return {
          statusCode: 400,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid JSON in request body" }),
        };
      }

      const username = body.username ? body.username.toString().trim() : "";
      const password = body.password ? body.password.toString().trim() : "";

      if (!username || !password) {
        return {
          statusCode: 400,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Username and password are required" }),
        };
      }

      secureLog("info", "Login attempt", { username, ip: clientIP });

      // Verify admin credentials
      const adminAccount = ADMIN_ACCOUNTS[username];
      if (adminAccount && adminAccount.password === password) {
        secureLog("info", "Login successful", { username, ip: clientIP });

        const token = jwt.sign(
          {
            username,
            role: adminAccount.role,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24 hours
          },
          ADMIN_JWT_SECRET,
        );

        return {
          statusCode: 200,
          headers: getSecurityHeaders(),
          body: JSON.stringify({
            token,
            user: {
              username,
              role: adminAccount.role,
            },
          }),
        };
      }
      secureLog("warn", "Login failed - invalid credentials", {
        username,
        ip: clientIP,
      });

      // Constant time delay to prevent timing attacks
      await new Promise((resolve) => setTimeout(resolve, 1000));

      return {
        statusCode: 401,
        headers: getSecurityHeaders(),
        body: JSON.stringify({ error: "Invalid credentials" }),
      };
    }

    // Get current user endpoint
    if (path === "/me" && event.httpMethod === "GET") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);

      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({
          username: admin.username,
          role: admin.role,
        }),
      };
    }

    // Public config endpoint for admin setup status
    if (path === "/config" && event.httpMethod === "GET") {
      const supabaseUrl = process.env.SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_KEY;
      const adminJwtSecret = process.env.ADMIN_JWT_SECRET;
      const admin1Username = process.env.ADMIN1_USERNAME;
      const admin1Password = process.env.ADMIN1_PASSWORD;

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({
          demo: false,
          configured: !!(
            supabaseUrl &&
            supabaseKey &&
            adminJwtSecret &&
            admin1Username &&
            admin1Password
          ),
          url: !!supabaseUrl,
          key: !!supabaseKey,
          adminJwtSecret: !!adminJwtSecret,
          adminCredentials: !!(admin1Username && admin1Password),
          environment: {
            hasSupabase: !!(supabaseUrl && supabaseKey),
            hasAdminAuth: !!(
              adminJwtSecret &&
              admin1Username &&
              admin1Password
            ),
            adminCount: Object.keys(ADMIN_ACCOUNTS).length,
          },
        }),
      };
    }

    // Get Supabase config endpoint (protected)
    if (path === "/supabase-config" && event.httpMethod === "GET") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);

      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      // Return Supabase config
      const supabaseUrl = process.env.SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_KEY;

      if (!supabaseUrl || !supabaseKey) {
        return {
          statusCode: 500,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Database not configured" }),
        };
      }

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({
          url: supabaseUrl,
          key: supabaseKey,
        }),
      };
    }

    // Protected admin endpoints
    const supabase = getSupabaseClient();

    // Generate new key endpoint
    if (path === "/keys/generate" && event.httpMethod === "POST") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);
      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      // Rate limiting for sensitive operations
      if (!checkRateLimit(clientIP, "api")) {
        return {
          statusCode: 429,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Too many requests" }),
        };
      }

      let body;
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return {
          statusCode: 400,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid JSON" }),
        };
      }

      const duration = parseInt(body.duration) || 24;
      const count = parseInt(body.count) || 1;

      if (count > 100) {
        return {
          statusCode: 400,
          headers: getSecurityHeaders(),
          body: JSON.stringify({
            error: "Cannot generate more than 100 keys at once",
          }),
        };
      }

      const keys = [];

      for (let i = 0; i < count; i++) {
        const key = generateKey();
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + duration);

        const { data, error } = await supabase
          .from("keys")
          .insert([
            {
              key_code: key,
              created_by: admin.username,
              created_at: new Date().toISOString(),
              expires_at: expiresAt.toISOString(),
              status: "active",
              ip_hash: crypto
                .createHash("sha256")
                .update(clientIP + "admin-generated")
                .digest("hex"),
              metadata: {},
            },
          ])
          .select()
          .single();

        if (error) {
          secureLog("error", "Failed to insert key", {
            error: error.message,
            admin: admin.username,
          });
          throw new Error("Database error during key generation");
        }

        keys.push(data);
      }

      secureLog("info", "Keys generated", {
        count: keys.length,
        duration,
        admin: admin.username,
        ip: clientIP,
      });

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({ keys }),
      };
    }

    // List keys endpoint
    if (path === "/keys" && event.httpMethod === "GET") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);
      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      const params = event.queryStringParameters || {};
      const page = parseInt(params.page) || 1;
      const limit = Math.min(parseInt(params.limit) || 25, 100);
      const search = params.search ? params.search.toString().trim() : "";
      const status = params.status ? params.status.toString().trim() : "all";
      const offset = (page - 1) * limit;

      let query = supabase
        .from("keys")
        .select("*", { count: "exact" })
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

      if (search) {
        query = query.or(`key_code.ilike.%${search}%,hwid.ilike.%${search}%`);
      }

      if (status !== "all") {
        query = query.eq("status", status);
      }

      const { data: keys, count, error } = await query;
      if (error) {
        secureLog("error", "Failed to fetch keys", { error: error.message });
        throw new Error("Database error");
      }

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({
          keys,
          total: count,
          page,
          limit,
        }),
      };
    }

    // Individual key extend endpoint
    if (path.match(/^\/keys\/(.+)\/extend$/) && event.httpMethod === "POST") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);
      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      const keyMatch = path.match(/^\/keys\/(.+)\/extend$/);
      const keyCode = keyMatch[1];

      let body;
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return {
          statusCode: 400,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid JSON" }),
        };
      }

      const hours = parseInt(body.hours) || 24;
      const newExpiry = new Date();
      newExpiry.setHours(newExpiry.getHours() + hours);

      const { data, error } = await supabase
        .from("keys")
        .update({
          expires_at: newExpiry.toISOString(),
          status: "active",
        })
        .eq("key_code", keyCode)
        .select()
        .single();

      if (error) {
        secureLog("error", "Failed to extend key", {
          error: error.message,
          keyCode,
        });
        return {
          statusCode: 404,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Key not found" }),
        };
      }

      secureLog("info", "Key extended", {
        keyCode,
        hours,
        admin: admin.username,
      });

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({
          message: `Key extended by ${hours} hours`,
          key: data,
        }),
      };
    }

    // Individual key revoke endpoint
    if (path.match(/^\/keys\/(.+)\/revoke$/) && event.httpMethod === "POST") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);
      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      const keyMatch = path.match(/^\/keys\/(.+)\/revoke$/);
      const keyCode = keyMatch[1];

      const { data, error } = await supabase
        .from("keys")
        .update({ status: "revoked" })
        .eq("key_code", keyCode)
        .select()
        .single();

      if (error) {
        secureLog("error", "Failed to revoke key", {
          error: error.message,
          keyCode,
        });
        return {
          statusCode: 404,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Key not found" }),
        };
      }

      secureLog("info", "Key revoked", { keyCode, admin: admin.username });

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({
          message: "Key revoked successfully",
          key: data,
        }),
      };
    }

    // Individual key reset HWID endpoint
    if (
      path.match(/^\/keys\/(.+)\/reset-hwid$/) &&
      event.httpMethod === "POST"
    ) {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);
      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      const keyMatch = path.match(/^\/keys\/(.+)\/reset-hwid$/);
      const keyCode = keyMatch[1];

      const { data, error } = await supabase
        .from("keys")
        .update({ hwid: null })
        .eq("key_code", keyCode)
        .select()
        .single();

      if (error) {
        secureLog("error", "Failed to reset HWID", {
          error: error.message,
          keyCode,
        });
        return {
          statusCode: 404,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Key not found" }),
        };
      }

      secureLog("info", "HWID reset", { keyCode, admin: admin.username });

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({
          message: "HWID reset successfully",
          key: data,
        }),
      };
    }

    // Delete key endpoint
    if (path.match(/^\/keys\/(.+)\/delete$/) && event.httpMethod === "DELETE") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);
      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      const keyMatch = path.match(/^\/keys\/(.+)\/delete$/);
      const keyCode = keyMatch[1];

      const { data, error } = await supabase
        .from("keys")
        .delete()
        .eq("key_code", keyCode)
        .select()
        .single();

      if (error) {
        secureLog("error", "Failed to delete key", {
          error: error.message,
          keyCode,
        });
        return {
          statusCode: 404,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Key not found" }),
        };
      }

      secureLog("info", "Key deleted", { keyCode, admin: admin.username });

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({
          message: "Key deleted successfully",
          key: data,
        }),
      };
    }

    // Analytics endpoint
    if (path === "/analytics" && event.httpMethod === "GET") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);
      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      const params = event.queryStringParameters || {};
      const period = params.period || "24h";

      let dateFrom = new Date();
      switch (period) {
        case "24h":
          dateFrom.setHours(dateFrom.getHours() - 24);
          break;
        case "7d":
          dateFrom.setDate(dateFrom.getDate() - 7);
          break;
        case "30d":
          dateFrom.setDate(dateFrom.getDate() - 30);
          break;
        default:
          dateFrom.setHours(dateFrom.getHours() - 24);
      }

      try {
        secureLog("info", "Analytics request started", {
          period,
          dateFrom: dateFrom.toISOString(),
        });

        // Get key generation analytics
        const { data: keyData, error: keyError } = await supabase
          .from("keys")
          .select("created_at, status")
          .gte("created_at", dateFrom.toISOString())
          .order("created_at", { ascending: true });

        if (keyError) {
          secureLog("error", "Key data fetch failed", {
            error: keyError.message,
          });
          throw keyError;
        }

        secureLog("info", "Key data fetched", { count: keyData?.length || 0 });

        // Get user session analytics
        const { data: sessionData, error: sessionError } = await supabase
          .from("user_sessions")
          .select("created_at, current_step, status")
          .gte("created_at", dateFrom.toISOString())
          .order("created_at", { ascending: true });

        if (sessionError) {
          secureLog("error", "Session data fetch failed", {
            error: sessionError.message,
          });
          throw sessionError;
        }

        secureLog("info", "Session data fetched", {
          count: sessionData?.length || 0,
        });

        // Process data for charts
        const keyGeneration = processTimeSeriesData(
          keyData || [],
          "created_at",
        );
        const userActivity = processTimeSeriesData(
          sessionData || [],
          "created_at",
        );

        const analytics = {
          keyGeneration,
          userActivity,
          summary: {
            totalKeys: keyData?.length || 0,
            activeSessions:
              sessionData?.filter((s) => s.status === "active").length || 0,
            completedSessions:
              sessionData?.filter((s) => s.current_step >= 3).length || 0,
          },
        };

        secureLog("info", "Analytics processed", {
          keyGenerationPoints: keyGeneration.length,
          userActivityPoints: userActivity.length,
          summary: analytics.summary,
        });

        return {
          statusCode: 200,
          headers: getSecurityHeaders(),
          body: JSON.stringify(analytics),
        };
      } catch (error) {
        secureLog("error", "Analytics fetch failed", {
          error: error.message,
          stack: error.stack,
        });
        return {
          statusCode: 500,
          headers: getSecurityHeaders(),
          body: JSON.stringify({
            error: "Failed to fetch analytics",
            details: error.message,
          }),
        };
      }
    }

    // Update key status endpoint
    if (path === "/keys/update" && event.httpMethod === "POST") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);
      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      let body;
      try {
        body = JSON.parse(event.body);
      } catch (e) {
        return {
          statusCode: 400,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid JSON" }),
        };
      }

      const key = body.key ? body.key.toString().trim() : "";
      const action = body.action ? body.action.toString().trim() : "";

      if (!key || !action) {
        return {
          statusCode: 400,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Key and action are required" }),
        };
      }

      let updateData = {};

      switch (action) {
        case "revoke":
          updateData = { status: "revoked" };
          break;
        case "extend":
          const hours = parseInt(body.hours) || 24;
          const newExpiry = new Date();
          newExpiry.setHours(newExpiry.getHours() + hours);
          updateData = {
            expires_at: newExpiry.toISOString(),
            status: "active",
          };
          break;
        case "reset-hwid":
          updateData = { hwid: null };
          break;
        default:
          return {
            statusCode: 400,
            headers: getSecurityHeaders(),
            body: JSON.stringify({ error: "Invalid action" }),
          };
      }

      const { data, error } = await supabase
        .from("keys")
        .update(updateData)
        .eq("key_code", key)
        .select()
        .single();

      if (error) {
        secureLog("error", "Failed to update key", {
          error: error.message,
          key,
          action,
        });
        throw new Error("Database error");
      }

      secureLog("info", "Key updated", { key, action, admin: admin.username });

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({ key: data }),
      };
    }

    // Get dashboard stats
    if (path === "/stats" && event.httpMethod === "GET") {
      const authHeader = event.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Unauthorized" }),
        };
      }

      const token = authHeader.split(" ")[1];
      const admin = verifyAdminToken(token);
      if (!admin) {
        return {
          statusCode: 401,
          headers: getSecurityHeaders(),
          body: JSON.stringify({ error: "Invalid token" }),
        };
      }

      const now = new Date().toISOString();
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const [
        { count: totalKeys },
        { count: activeKeys },
        { count: expiredKeys },
        { count: todayKeys },
        { data: recentKeys },
      ] = await Promise.all([
        supabase.from("keys").select("*", { count: "exact", head: true }),
        supabase
          .from("keys")
          .select("*", { count: "exact", head: true })
          .eq("status", "active")
          .gt("expires_at", now),
        supabase
          .from("keys")
          .select("*", { count: "exact", head: true })
          .lt("expires_at", now),
        supabase
          .from("keys")
          .select("*", { count: "exact", head: true })
          .gte("created_at", today.toISOString()),
        supabase
          .from("keys")
          .select("*")
          .order("created_at", { ascending: false })
          .limit(5),
      ]);

      return {
        statusCode: 200,
        headers: getSecurityHeaders(),
        body: JSON.stringify({
          stats: {
            totalKeys: totalKeys || 0,
            activeKeys: activeKeys || 0,
            expiredKeys: expiredKeys || 0,
            todayKeys: todayKeys || 0,
            usageRate:
              totalKeys > 0 ? Math.round((activeKeys / totalKeys) * 100) : 0,
          },
          recentKeys: recentKeys || [],
        }),
      };
    }

    return {
      statusCode: 404,
      headers: getSecurityHeaders(),
      body: JSON.stringify({ error: "Not found" }),
    };
  } catch (error) {
    secureLog("error", "Admin function error", {
      error: error.message,
      path,
      method: event.httpMethod,
      ip: clientIP,
    });

    return {
      statusCode: 500,
      headers: getSecurityHeaders(),
      body: JSON.stringify({ error: "Server error" }),
    };
  }
};

// Helper function to process time series data for analytics
function processTimeSeriesData(data, dateField) {
  const groupedData = {};

  data.forEach((item) => {
    const date = new Date(item[dateField]);
    const dateKey = date.toISOString().split("T")[0]; // Group by day

    if (!groupedData[dateKey]) {
      groupedData[dateKey] = 0;
    }
    groupedData[dateKey]++;
  });

  // Convert to array format for charts
  return Object.entries(groupedData)
    .map(([date, count]) => ({
      date,
      count,
    }))
    .sort((a, b) => new Date(a.date) - new Date(b.date));
}
