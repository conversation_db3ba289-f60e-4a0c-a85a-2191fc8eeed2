const { createClient } = require("@supabase/supabase-js");

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

let supabase = null;

if (!supabaseUrl || !supabaseKey) {
  console.error("Supabase environment variables not found. Please configure SUPABASE_URL and SUPABASE_KEY");
  throw new Error("Database configuration missing");
} else {
  try {
    supabase = createClient(supabaseUrl, supabaseKey);
    console.log("Supabase client initialized successfully");
  } catch (error) {
    console.error("Failed to initialize Supabase:", error);
    throw new Error("Failed to initialize database connection");
  }
}

// Database Operations
class Database {
  constructor() {
    this.supabase = supabase;
  }

  // Key Management
  async createKey(keyData) {
    const { data, error } = await this.supabase
      .from("keys")
      .insert([
        {
          key_code: keyData.key,
          ip_hash: keyData.ipHash,
          session_id: keyData.sessionId,
          created_by: keyData.createdBy || "user",
          expires_at: keyData.expiresAt,
          metadata: keyData.metadata || {},
          status: 'active'
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error creating key:", error);
      throw new Error("Failed to create key: " + error.message);
    }

    console.log("Key created successfully:", data.key_code);
    return data;
  }

  async getKey(keyCode) {
    const { data, error } = await this.supabase
      .from("keys")
      .select("*")
      .eq("key_code", keyCode)
      .single();

    if (error && error.code !== "PGRST116") {
      console.error("Error fetching key:", error);
      throw new Error("Failed to fetch key: " + error.message);
    }

    return data;
  }

  async updateKey(keyCode, updates) {
    const { data, error } = await this.supabase
      .from("keys")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("key_code", keyCode)
      .select()
      .single();

    if (error) {
      console.error("Error updating key:", error);
      throw new Error("Failed to update key: " + error.message);
    }

    return data;
  }

  async validateAndUseKey(keyCode, hwid) {
    const key = await this.getKey(keyCode);

    if (!key) {
      return { valid: false, reason: "Key not found" };
    }

    // Check if key is expired
    if (new Date(key.expires_at) < new Date()) {
      return { valid: false, reason: "Key has expired" };
    }

    // Check if key is active
    if (key.status !== "active") {
      return { valid: false, reason: "Key is not active" };
    }

    // HWID binding logic
    if (!key.hwid) {
      // First time use - bind to this HWID
      await this.updateKey(keyCode, {
        hwid: hwid,
        last_used_at: new Date().toISOString(),
        usage_count: (key.usage_count || 0) + 1,
      });
      return { valid: true, reason: "Key validated and bound to device" };
    } else if (key.hwid === hwid) {
      // HWID matches - update usage
      await this.updateKey(keyCode, {
        last_used_at: new Date().toISOString(),
        usage_count: (key.usage_count || 0) + 1,
      });
      return { valid: true, reason: "Key validated successfully" };
    } else {
      // HWID mismatch
      return { valid: false, reason: "Key is bound to a different device" };
    }
  }

  async getKeys(filters = {}) {
    let query = this.supabase.from("keys").select("*", { count: "exact" });

    // Apply filters
    if (filters.status) {
      query = query.eq("status", filters.status);
    }

    if (filters.search) {
      query = query.or(
        `key_code.ilike.%${filters.search}%,hwid.ilike.%${filters.search}%,created_by.ilike.%${filters.search}%`,
      );
    }

    if (filters.dateFrom) {
      query = query.gte("created_at", filters.dateFrom);
    }

    if (filters.dateTo) {
      query = query.lte("created_at", filters.dateTo);
    }

    // Pagination
    const page = filters.page || 1;
    const limit = Math.min(filters.limit || 25, 100);
    const offset = (page - 1) * limit;

    query = query
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, count, error } = await query;

    if (error) {
      console.error("Error fetching keys:", error);
      throw new Error("Failed to fetch keys: " + error.message);
    }

    return {
      keys: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit),
    };
  }

  async deleteExpiredKeys() {
    const { data, error } = await this.supabase
      .from("keys")
      .delete()
      .lt("expires_at", new Date().toISOString())
      .select();

    if (error) {
      console.error("Error deleting expired keys:", error);
      throw new Error("Failed to delete expired keys: " + error.message);
    }

    return data?.length || 0;
  }

  async hasGeneratedKeyToday(ipHash) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const { data, error } = await this.supabase
      .from("keys")
      .select("id")
      .eq("ip_hash", ipHash)
      .gte("created_at", today.toISOString())
      .limit(1);

    if (error) {
      console.error("Error checking daily key limit:", error);
      throw new Error("Failed to check daily key limit: " + error.message);
    }

    return data && data.length > 0;
  }

  // Session Management
  async createSession(sessionData) {
    const { data, error } = await this.supabase
      .from("user_sessions")
      .insert([
        {
          token: sessionData.token,
          ip_hash: sessionData.ipHash,
          service: sessionData.service || 'linkvertise',
          expires_at: sessionData.expiresAt,
          verification_data: sessionData.verificationData || {},
          current_step: 1,
          status: 'active'
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error creating session:", error);
      throw new Error("Failed to create session: " + error.message);
    }

    return data;
  }

  async getSession(token) {
    const { data, error } = await this.supabase
      .from("user_sessions")
      .select("*")
      .eq("token", token)
      .eq("status", "active")
      .single();

    if (error && error.code !== "PGRST116") {
      console.error("Error fetching session:", error);
      throw new Error("Failed to fetch session: " + error.message);
    }

    return data;
  }

  async updateSession(token, updates) {
    const { data, error } = await this.supabase
      .from("user_sessions")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("token", token)
      .select()
      .single();

    if (error) {
      console.error("Error updating session:", error);
      throw new Error("Failed to update session: " + error.message);
    }

    return data;
  }

  async deleteSession(token) {
    const { error } = await this.supabase
      .from("user_sessions")
      .delete()
      .eq("token", token);

    if (error) {
      console.error("Error deleting session:", error);
      throw new Error("Failed to delete session: " + error.message);
    }
  }

  async cleanupExpiredSessions() {
    const { data, error } = await this.supabase
      .from("user_sessions")
      .delete()
      .lt("expires_at", new Date().toISOString())
      .select();

    if (error) {
      console.error("Error cleaning up expired sessions:", error);
      throw new Error("Failed to cleanup expired sessions: " + error.message);
    }

    return data?.length || 0;
  }

  // Admin Logging
  async logAdminAction(logData) {
    const { error } = await this.supabase.from("admin_logs").insert([
      {
        admin_username: logData.admin,
        action: logData.action,
        target_type: logData.targetType,
        target_id: logData.targetId,
        details: logData.details || {},
        ip_address: logData.ipAddress,
      },
    ]);

    if (error) {
      console.error("Error logging admin action:", error);
    }
  }

  async getAdminLogs(filters = {}) {
    let query = this.supabase
      .from("admin_logs")
      .select("*", { count: "exact" });

    if (filters.admin) {
      query = query.eq("admin_username", filters.admin);
    }

    if (filters.action) {
      query = query.eq("action", filters.action);
    }

    if (filters.dateFrom) {
      query = query.gte("created_at", filters.dateFrom);
    }

    if (filters.dateTo) {
      query = query.lte("created_at", filters.dateTo);
    }

    const page = filters.page || 1;
    const limit = Math.min(filters.limit || 50, 100);
    const offset = (page - 1) * limit;

    query = query
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, count, error } = await query;

    if (error) {
      console.error("Error fetching admin logs:", error);
      throw new Error("Failed to fetch admin logs: " + error.message);
    }

    return {
      logs: data || [],
      total: count || 0,
      page,
      limit,
    };
  }

  // Statistics
  async getSystemStats() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [
      totalKeys,
      activeKeys,
      todayKeys,
      weekKeys,
      monthKeys,
      usedKeys,
      expiredKeys,
    ] = await Promise.all([
      this.supabase.from("keys").select("id", { count: "exact", head: true }),
      this.supabase
        .from("keys")
        .select("id", { count: "exact", head: true })
        .eq("status", "active")
        .gt("expires_at", now.toISOString()),
      this.supabase
        .from("keys")
        .select("id", { count: "exact", head: true })
        .gte("created_at", today.toISOString()),
      this.supabase
        .from("keys")
        .select("id", { count: "exact", head: true })
        .gte("created_at", thisWeek.toISOString()),
      this.supabase
        .from("keys")
        .select("id", { count: "exact", head: true })
        .gte("created_at", thisMonth.toISOString()),
      this.supabase
        .from("keys")
        .select("id", { count: "exact", head: true })
        .not("hwid", "is", null),
      this.supabase
        .from("keys")
        .select("id", { count: "exact", head: true })
        .lt("expires_at", now.toISOString()),
    ]);

    return {
      total: totalKeys.count || 0,
      active: activeKeys.count || 0,
      used: usedKeys.count || 0,
      expired: expiredKeys.count || 0,
      today: todayKeys.count || 0,
      thisWeek: weekKeys.count || 0,
      thisMonth: monthKeys.count || 0,
      usageRate:
        totalKeys.count > 0
          ? Math.round((usedKeys.count / totalKeys.count) * 100)
          : 0,
    };
  }

  async getRecentActivity(limit = 10) {
    const { data, error } = await this.supabase
      .from("keys")
      .select("key_code, created_at, created_by, status, hwid, usage_count")
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching recent activity:", error);
      return [];
    }

    return data || [];
  }

  // Script Management for Kill Switch
  async getScript(scriptId) {
    try {
      const { data, error } = await this.supabase
        .from("scripts")
        .select("*")
        .eq("script_id", scriptId)
        .single();

      if (error && error.code !== "PGRST116") {
        // Check if table doesn't exist
        if (error.code === '42P01' || error.message.includes('relation "scripts" does not exist')) {
          console.warn("Scripts table does not exist yet");
          return null;
        }
        console.error("Error fetching script:", error);
        throw new Error("Failed to fetch script: " + error.message);
      }

      return data;
    } catch (error) {
      console.warn("Database error:", error);
      return null;
    }
  }

  async getAllScripts() {
    try {
      const { data, error } = await this.supabase
        .from("scripts")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        // Check if table doesn't exist
        if (error.code === '42P01' || error.message.includes('relation "scripts" does not exist')) {
          console.warn("Scripts table does not exist yet");
          return [];
        }
        console.error("Error fetching scripts:", error);
        throw new Error("Failed to fetch scripts: " + error.message);
      }

      return data || [];
    } catch (error) {
      console.warn("Database error, returning empty array:", error);
      return [];
    }
  }

  async updateScriptStatus(scriptId, updates) {
    const { data, error } = await this.supabase
      .from("scripts")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("script_id", scriptId)
      .select()
      .single();

    if (error) {
      console.error("Error updating script:", error);
      throw new Error("Failed to update script: " + error.message);
    }

    return data;
  }

  async updateScriptUsage(scriptId) {
    const { error } = await this.supabase
      .from("scripts")
      .update({
        usage_count: this.supabase.rpc('increment_usage', { script_id: scriptId }),
        last_heartbeat: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq("script_id", scriptId);

    if (error) {
      console.error("Error updating script usage:", error);
    }
  }

  async logScriptUsage(scriptId, hwid, key, action, message = null) {
    try {
      const { error } = await this.supabase
        .from("script_usage")
        .insert([{
          script_id: scriptId,
          user_hwid: hwid,
          user_key: key,
          action: action,
          status: action === 'success' ? 'success' : (action === 'blocked' ? 'blocked' : 'error'),
          error_message: message,
          ip_hash: null, // Will be set by calling function if needed
          metadata: {},
        }]);

      if (error) {
        // Check if table doesn't exist
        if (error.code === '42P01' || error.message.includes('relation "script_usage" does not exist')) {
          console.warn("Script usage table does not exist yet");
          return;
        }
        console.error("Error logging script usage:", error);
      }
    } catch (error) {
      console.warn("Database error while logging:", error);
    }
  }

  async getSystemSetting(key) {
    try {
      const { data, error } = await this.supabase
        .from("system_settings")
        .select("setting_value")
        .eq("setting_key", key)
        .single();

      if (error && error.code !== "PGRST116") {
        // Check if table doesn't exist
        if (error.code === '42P01' || error.message.includes('relation "system_settings" does not exist')) {
          console.warn("System settings table does not exist yet");
          return null;
        }
        console.error("Error fetching system setting:", error);
        return null;
      }

      return data ? JSON.parse(data.setting_value) : null;
    } catch (error) {
      console.warn("Database error:", error);
      return null;
    }
  }

  async getSystemSettings() {
    try {
      const { data, error } = await this.supabase
        .from("system_settings")
        .select("*")
        .order("setting_key");

      if (error) {
        // Check if table doesn't exist
        if (error.code === '42P01' || error.message.includes('relation "system_settings" does not exist')) {
          console.warn("System settings table does not exist yet");
          return [];
        }
        console.error("Error fetching system settings:", error);
        throw new Error("Failed to fetch system settings: " + error.message);
      }

      return data || [];
    } catch (error) {
      console.warn("Database error, returning empty array:", error);
      return [];
    }
  }

  async updateSystemSetting(key, value) {
    const { data, error } = await this.supabase
      .from("system_settings")
      .update({
        setting_value: JSON.stringify(value),
        updated_at: new Date().toISOString(),
      })
      .eq("setting_key", key)
      .select()
      .single();

    if (error) {
      console.error("Error updating system setting:", error);
      throw new Error("Failed to update system setting: " + error.message);
    }

    return data;
  }

  async createScript(scriptData) {
    const { data, error } = await this.supabase
      .from("scripts")
      .insert([{
        script_name: scriptData.name,
        script_id: scriptData.id,
        status: scriptData.status || 'active',
        description: scriptData.description || '',
        version: scriptData.version || '1.0.0',
        script_url: scriptData.url || '',
        kill_switch_enabled: scriptData.killSwitchEnabled || true,
        maintenance_message: scriptData.maintenanceMessage || 'Script is currently under maintenance',
        allowed_users: scriptData.allowedUsers || [],
        metadata: scriptData.metadata || {},
      }])
      .select()
      .single();

    if (error) {
      console.error("Error creating script:", error);
      throw new Error("Failed to create script: " + error.message);
    }

    return data;
  }

  async deleteScript(scriptId) {
    const { data, error } = await this.supabase
      .from("scripts")
      .delete()
      .eq("script_id", scriptId)
      .select();

    if (error) {
      console.error("Error deleting script:", error);
      throw new Error("Failed to delete script: " + error.message);
    }

    return data;
  }

  async getScriptUsageLogs(scriptId, limit = 100) {
    const { data, error } = await this.supabase
      .from("script_usage")
      .select("*")
      .eq("script_id", scriptId)
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching script usage logs:", error);
      throw new Error("Failed to fetch script usage logs: " + error.message);
    }

    return data || [];
  }

  async getActiveScriptSessions(scriptId) {
    const { data, error } = await this.supabase
      .from("script_sessions")
      .select("*")
      .eq("script_id", scriptId)
      .eq("status", "active")
      .order("started_at", { ascending: false });

    if (error) {
      console.error("Error fetching script sessions:", error);
      throw new Error("Failed to fetch script sessions: " + error.message);
    }

    return data || [];
  }

  async terminateScriptSession(sessionToken) {
    const { data, error } = await this.supabase
      .from("script_sessions")
      .update({
        status: "terminated",
        updated_at: new Date().toISOString(),
      })
      .eq("session_token", sessionToken)
      .select();

    if (error) {
      console.error("Error terminating script session:", error);
      throw new Error("Failed to terminate script session: " + error.message);
    }

    return data;
  }

  async createScriptSession(sessionData) {
    const { data, error } = await this.supabase
      .from("script_sessions")
      .insert([{
        script_id: sessionData.scriptId,
        user_hwid: sessionData.hwid,
        user_key: sessionData.key,
        session_token: sessionData.sessionToken,
        ip_hash: sessionData.ipHash,
        expires_at: sessionData.expiresAt,
        metadata: sessionData.metadata || {},
      }])
      .select()
      .single();

    if (error) {
      console.error("Error creating script session:", error);
      throw new Error("Failed to create script session: " + error.message);
    }

    return data;
  }

  async updateScriptSession(sessionToken, updates) {
    const { data, error } = await this.supabase
      .from("script_sessions")
      .update({
        ...updates,
        last_heartbeat: new Date().toISOString(),
      })
      .eq("session_token", sessionToken)
      .select();

    if (error) {
      console.error("Error updating script session:", error);
      throw new Error("Failed to update script session: " + error.message);
    }

    return data;
  }

  async getScriptUsageStats(scriptId) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [totalUsage, todayUsage, weekUsage, blockedUsage] = await Promise.all([
      this.supabase
        .from("script_usage")
        .select("id", { count: "exact", head: true })
        .eq("script_id", scriptId),
      this.supabase
        .from("script_usage")
        .select("id", { count: "exact", head: true })
        .eq("script_id", scriptId)
        .gte("created_at", today.toISOString()),
      this.supabase
        .from("script_usage")
        .select("id", { count: "exact", head: true })
        .eq("script_id", scriptId)
        .gte("created_at", thisWeek.toISOString()),
      this.supabase
        .from("script_usage")
        .select("id", { count: "exact", head: true })
        .eq("script_id", scriptId)
        .eq("status", "blocked"),
    ]);

    return {
      total: totalUsage.count || 0,
      today: todayUsage.count || 0,
      thisWeek: weekUsage.count || 0,
      blocked: blockedUsage.count || 0,
    };
  }

  // Rate Limiting
  async checkRateLimit(
    identifier,
    action,
    maxAttempts = 5,
    windowMinutes = 15,
  ) {
    const windowStart = new Date(Date.now() - windowMinutes * 60 * 1000);

    // Clean up old entries
    await this.supabase
      .from("rate_limits")
      .delete()
      .lt("window_start", windowStart.toISOString());

    // Check current attempts
    const { data, error } = await this.supabase
      .from("rate_limits")
      .select("*")
      .eq("identifier", identifier)
      .eq("action", action)
      .gte("window_start", windowStart.toISOString())
      .single();

    if (error && error.code !== "PGRST116") {
      console.error("Error checking rate limit:", error);
      return true; // Allow on error
    }

    if (!data) {
      // First attempt in this window
      await this.supabase.from("rate_limits").insert([
        {
          identifier,
          action,
          attempts: 1,
          window_start: new Date().toISOString(),
        },
      ]);
      return true;
    }

    if (data.attempts >= maxAttempts) {
      return false; // Rate limited
    }

    // Increment attempts
    await this.supabase
      .from("rate_limits")
      .update({ attempts: data.attempts + 1 })
      .eq("id", data.id);

    return true;
  }
}

// Export singleton instance
const database = new Database();

module.exports = {
  supabase,
  database,
  // Legacy exports for backward compatibility
  hasGeneratedKeyToday: (ipHash) => database.hasGeneratedKeyToday(ipHash),
  getTokenData: async (token) => {
    try {
      const data = await database.getSession(token);
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },
  storeToken: async (token, expiresAt, ipHash) => {
    try {
      const data = await database.createSession({
        token,
        expiresAt: typeof expiresAt === 'string' ? expiresAt : expiresAt.toISOString(),
        ipHash,
        service: 'linkvertise'
      });
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },
  updateTokenStep: async (tokenId, step, updateData) => {
    try {
      const data = await database.updateSession(tokenId, {
        current_step: step,
        ...updateData
      });
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },
  isTokenValid: async (tokenId) => {
    try {
      const session = await database.getSession(tokenId);
      return session && new Date(session.expires_at) > new Date();
    } catch (error) {
      console.error("Error checking token validity:", error);
      return false;
    }
  },
  trackLinkVisit: async (tokenId, service) => {
    try {
      await database.updateSession(tokenId, {
        service: service,
        link_visit_time: new Date().toISOString()
      });
      return { error: null };
    } catch (error) {
      return { error };
    }
  },
  deleteToken: (token) => database.deleteSession(token),
  cleanupExpiredTokens: () => database.cleanupExpiredSessions(),
  storeKey: (key, sessionId, ipHash, expiresAt) =>
    database.createKey({
      key,
      sessionId,
      ipHash,
      expiresAt,
    }),
  getKeyData: (key) => database.getKey(key),
  updateKeyHwid: (key, hwid) => database.updateKey(key, { hwid }),
  resetKeyHwid: (key) => database.updateKey(key, { hwid: null }),
  checkKeyInDatabase: (key, hwid) => database.validateAndUseKey(key, hwid),
  checkRateLimit: (identifier, action) =>
    database.checkRateLimit(identifier, action),
};