const crypto = require('crypto');
const { supabase } = require('./database');

// Enhanced link verification with bypass detection
async function verifyLinkCompletion(tokenId, service, verificationData) {
  try {
    const { data: tokenData, error } = await supabase
      .from('user_progression')
      .select('*')
      .eq('token', tokenId)
      .single();
    
    if (error || !tokenData) {
      return { valid: false, reason: 'Invalid token' };
    }
    
    const timeOnLink = new Date() - new Date(tokenData.link_visit_time || tokenData.created_at);
    const minimumTime = 8000; // 8 seconds

    if (timeOnLink < minimumTime) {
      return {
        valid: false,
        reason: `Please spend at least 8 seconds on the ${service} link. Time remaining: ${Math.ceil((minimumTime - timeOnLink) / 1000)}s`
      };
    }
    
    // Valid referrers for each service
    const validReferrers = {
      'linkvertise': ['linkvertise.com', 'link-center.net', 'link-target.net', 'link-hub.net'],
      'shrinkme': ['shrinkme.io', 'shrinkme.org']
    };
    
    const referrer = verificationData.referrer || '';
    const serviceReferrers = validReferrers[service] || [];
    const hasValidReferrer = serviceReferrers.some(domain => referrer.includes(domain));
    
    // Enhanced bypass detection
    const bypassIndicators = [
      'bypasscity.com', 'bypass.city', 'bypassvip.com',
      'linkvertise.net', 'shrink-me.io', 'short-link.me',
      'direct-link', 'bypass', 'skip', 'free', 'unlock',
      'generator', 'hack', 'cheat', 'bot'
    ];
    
    const isBypass = bypassIndicators.some(indicator => 
      referrer.toLowerCase().includes(indicator) || 
      (verificationData.userAgent || '').toLowerCase().includes(indicator)
    );
    
    if (isBypass) {
      return { 
        valid: false, 
        reason: 'Bypass service detected. Please use the official link shortener.',
        bypassDetected: true
      };
    }
    
    // Check for too many recent attempts from same IP
    const ipHash = verificationData.ipHash;
    const recentVerifications = await supabase
      .from('user_progression')
      .select('created_at')
      .eq('ip_hash', ipHash)
      .gte('created_at', new Date(Date.now() - 300000).toISOString()) // Last 5 minutes
      .neq('token', tokenId);
    
    if (recentVerifications.data && recentVerifications.data.length > 3) {
      return { 
        valid: false, 
        reason: 'Too many verification attempts. Please wait 5 minutes.' 
      };
    }
    
    // Calculate interaction score
    const interactionScore = calculateInteractionScore(verificationData);
    if (interactionScore < 3) {
      return { 
        valid: false, 
        reason: 'Insufficient interaction detected. Please properly complete the link.' 
      };
    }
    
    return { valid: true, reason: 'Link completion verified' };
    
  } catch (error) {
    console.error('Link verification error:', error);
    return { valid: false, reason: 'Verification error' };
  }
}

function calculateInteractionScore(data) {
  let score = 0;

  // Time spent bonus
  const timeSpent = data.timeSpent || 0;
  score += Math.min(Math.floor(timeSpent / 3000), 3);

  // Valid referrer bonus
  if (data.hasValidReferrer) score += 2;

  // Mouse interaction bonus
  if (data.mouseInteractions > 3) score += 1;

  // Screen resolution check
  const screenWidth = data.screenWidth || 0;
  const screenHeight = data.screenHeight || 0;
  if (screenWidth >= 800 && screenHeight >= 600 && screenWidth <= 3840 && screenHeight <= 2160) {
    score += 1;
  }

  // User agent check
  const userAgent = data.userAgent || '';
  if (userAgent.includes('Chrome') || userAgent.includes('Firefox') || userAgent.includes('Safari')) {
    if (!userAgent.includes('Headless') && !userAgent.includes('PhantomJS')) {
      score += 1;
    }
  }

  return score;
}

// Security headers
function getHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block'
  };
}

function createResponse(statusCode, body) {
  return {
    statusCode,
    headers: getHeaders(),
    body: JSON.stringify(body)
  };
}

// Get client IP with proper fallbacks
function getClientIP(event) {
  const headers = event.headers || {};
  const ipHeaders = [
    'cf-connecting-ip', // Cloudflare
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'x-nf-client-connection-ip' // Netlify
  ];

  for (const header of ipHeaders) {
    const value = headers[header];
    if (value) {
      const ip = value.split(',')[0].trim();
      if (isValidIP(ip)) {
        return ip;
      }
    }
  }

  const remoteAddress = event.requestContext?.identity?.sourceIp;
  if (remoteAddress && isValidIP(remoteAddress)) {
    return remoteAddress;
  }

  return '127.0.0.1';
}

function isValidIP(ip) {
  if (!ip || typeof ip !== 'string') return false;

  // IPv4 validation
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  if (ipv4Regex.test(ip)) return true;

  // IPv6 validation (simplified)
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
  if (ipv6Regex.test(ip)) return true;

  return false;
}

// Hash IP for privacy
function hashIP(ip) {
  const ipToHash = ip || 'unknown';
  const salt = process.env.IP_HASH_SALT || 'default-salt';
  const hash = crypto.createHash('sha256').update(ipToHash + salt).digest('hex');
  return hash;
}

// Enhanced fingerprint verification
async function verifyFingerprint(storedFingerprint, currentFingerprint) {
  try {
    const stored = JSON.parse(atob(storedFingerprint));
    const current = JSON.parse(atob(currentFingerprint));

    // Core fingerprint elements that should remain consistent
    const coreElements = [
      'userAgent',
      'language',
      'platform',
      'timezone',
      'screen'
    ];

    let matchScore = 0;
    let totalElements = coreElements.length;

    for (const element of coreElements) {
      if (stored[element] === current[element]) {
        matchScore++;
      }
    }

    // Allow for some variance (80% match threshold)
    const matchPercentage = matchScore / totalElements;

    // Additional checks for suspicious patterns
    const timeDiff = current.timestamp - stored.timestamp;
    const maxTimeDiff = 30 * 60 * 1000; // 30 minutes

    if (timeDiff > maxTimeDiff) {
      // If too much time has passed, require higher match threshold
      return matchPercentage >= 0.9;
    }

    return matchPercentage >= 0.8;
  } catch (error) {
    console.error('Fingerprint verification error:', error);
    return false;
  }
}

// Advanced bypass detection
function detectAdvancedBypass(verificationData) {
  const suspiciousPatterns = [
    // Automation tools
    'selenium', 'webdriver', 'puppeteer', 'playwright', 'chromedriver',
    // Headless browsers
    'headless', 'phantomjs', 'htmlunit',
    // Bot frameworks
    'bot', 'crawler', 'spider', 'scraper',
    // Bypass tools
    'bypass', 'skip', 'unlock', 'free', 'generator', 'hack', 'cheat',
    // Virtual environments
    'virtual', 'vm', 'qemu', 'virtualbox', 'vmware'
  ];

  const userAgent = (verificationData.userAgent || '').toLowerCase();
  const referrer = (verificationData.referrer || '').toLowerCase();

  // Check for suspicious patterns
  for (const pattern of suspiciousPatterns) {
    if (userAgent.includes(pattern) || referrer.includes(pattern)) {
      return {
        detected: true,
        reason: `Suspicious pattern detected: ${pattern}`,
        confidence: 0.9
      };
    }
  }

  // Check for impossible screen resolutions
  const screenWidth = verificationData.screenWidth || 0;
  const screenHeight = verificationData.screenHeight || 0;

  if (screenWidth < 320 || screenHeight < 240 ||
      screenWidth > 7680 || screenHeight > 4320) {
    return {
      detected: true,
      reason: 'Impossible screen resolution',
      confidence: 0.8
    };
  }

  // Check for missing or suspicious WebGL
  if (!verificationData.webgl || verificationData.webgl === 'no-webgl') {
    return {
      detected: true,
      reason: 'Missing WebGL support (possible headless browser)',
      confidence: 0.7
    };
  }

  return { detected: false };
}

module.exports = {
  verifyLinkCompletion,
  calculateInteractionScore,
  getHeaders,
  createResponse,
  getClientIP,
  hashIP,
  isValidIP,
  verifyFingerprint,
  detectAdvancedBypass
};