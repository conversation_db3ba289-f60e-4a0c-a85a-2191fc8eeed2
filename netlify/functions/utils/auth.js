const crypto = require('crypto');
const jwt = require('jsonwebtoken');

const JWT_SECRET = process.env.JWT_SECRET || 'default-secret-change-in-production';

function generateSecureToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { 
    expiresIn: '30m',
    jwtid: crypto.randomBytes(16).toString('hex')
  });
}

function verifySecureToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    console.error('Token verification failed:', error.message);
    return null;
  }
}

async function verifyRecaptcha(recaptchaResponse) {
  const secretKey = process.env.RECAPTCHA_SECRET_KEY;

  if (!secretKey) {
    console.error('RECAPTCHA_SECRET_KEY environment variable not set');
    throw new Error('reCAPTCHA not configured');
  }

  if (!recaptchaResponse || typeof recaptchaResponse !== 'string' || recaptchaResponse.length < 10) {
    return false;
  }

  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: `secret=${secretKey}&response=${recaptchaResponse}`
    });

    if (!response.ok) {
      console.error('reCAPTCHA API request failed:', response.status);
      return false;
    }

    const data = await response.json();

    if (data['error-codes'] && data['error-codes'].length > 0) {
      console.error('reCAPTCHA verification failed:', data['error-codes']);
      return false;
    }

    return data.success === true;
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return false;
  }
}

function generateToken() {
  return crypto.randomBytes(32).toString('hex');
}

function generateKey() {
  const prefix = "PL-";
  const segments = 3;
  const segmentLength = 4;
  
  let key = prefix;
  
  for (let i = 0; i < segments; i++) {
    if (i > 0) key += "-";
    key += crypto.randomBytes(segmentLength)
      .toString('hex')
      .toUpperCase()
      .substring(0, segmentLength);
  }
  
  // Add checksum
  const checksum = crypto.createHash('md5')
    .update(key.replace(/-/g, '') + 'PROJECT_L_SALT')
    .digest('hex')
    .substring(0, 2)
    .toUpperCase();
  
  return key + "-" + checksum;
}

module.exports = {
  generateSecureToken,
  verifySecureToken,
  verifyRecaptcha,
  generateToken,
  generateKey
};