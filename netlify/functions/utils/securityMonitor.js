const crypto = require('crypto');
const { supabase } = require('./database');

// Real-time security monitoring and threat detection
class SecurityMonitor {
  constructor() {
    this.suspiciousActivities = new Map();
    this.ipReputation = new Map();
    this.deviceFingerprints = new Map();
    this.behaviorPatterns = new Map();
  }

  // Advanced threat detection
  async detectThreat(sessionData) {
    const threats = [];
    
    // 1. Rapid progression detection
    const rapidProgression = this.detectRapidProgression(sessionData);
    if (rapidProgression.detected) {
      threats.push(rapidProgression);
    }

    // 2. Impossible timing detection
    const impossibleTiming = this.detectImpossibleTiming(sessionData);
    if (impossibleTiming.detected) {
      threats.push(impossibleTiming);
    }

    // 3. Device switching detection
    const deviceSwitching = await this.detectDeviceSwitching(sessionData);
    if (deviceSwitching.detected) {
      threats.push(deviceSwitching);
    }

    // 4. Behavioral anomaly detection
    const behaviorAnomaly = this.detectBehaviorAnomaly(sessionData);
    if (behaviorAnomaly.detected) {
      threats.push(behaviorAnomaly);
    }

    // 5. Network-based detection
    const networkThreat = await this.detectNetworkThreats(sessionData);
    if (networkThreat.detected) {
      threats.push(networkThreat);
    }

    return {
      threatLevel: this.calculateThreatLevel(threats),
      threats: threats,
      action: this.determineAction(threats)
    };
  }

  // Detect suspiciously rapid step progression
  detectRapidProgression(sessionData) {
    const { tokenId, currentStep, stepStartTime } = sessionData;
    const timeSpent = Date.now() - stepStartTime;
    
    // Minimum realistic times for each step
    const minimumTimes = {
      1: 10000,  // 10 seconds for step 1
      2: 15000,  // 15 seconds for step 2
      3: 8000    // 8 seconds for step 3
    };

    if (timeSpent < minimumTimes[currentStep]) {
      return {
        detected: true,
        type: 'rapid_progression',
        severity: 'high',
        details: `Step ${currentStep} completed in ${timeSpent}ms (minimum: ${minimumTimes[currentStep]}ms)`,
        confidence: 0.9
      };
    }

    return { detected: false };
  }

  // Detect impossible timing patterns
  detectImpossibleTiming(sessionData) {
    const { mouseInteractions, timeSpent, screenWidth, screenHeight } = sessionData;
    
    // Calculate expected interaction rate for human behavior
    const interactionRate = mouseInteractions / (timeSpent / 1000);
    
    // Humans typically have 0.5-5 interactions per second
    if (interactionRate > 10 || (interactionRate === 0 && timeSpent > 5000)) {
      return {
        detected: true,
        type: 'impossible_timing',
        severity: 'high',
        details: `Interaction rate: ${interactionRate.toFixed(2)}/sec`,
        confidence: 0.85
      };
    }

    // Check for impossible screen interactions
    if (mouseInteractions > 0 && (screenWidth < 320 || screenHeight < 240)) {
      return {
        detected: true,
        type: 'impossible_screen',
        severity: 'high',
        details: `Mouse interactions on impossible screen size: ${screenWidth}x${screenHeight}`,
        confidence: 0.95
      };
    }

    return { detected: false };
  }

  // Detect device switching mid-session
  async detectDeviceSwitching(sessionData) {
    const { tokenId, fingerprint } = sessionData;
    
    if (!fingerprint) return { detected: false };

    try {
      // Get stored fingerprint from database
      const { data: session } = await supabase
        .from('user_sessions')
        .select('verification_data')
        .eq('token', tokenId)
        .single();

      if (session?.verification_data?.fingerprint) {
        const storedFingerprint = session.verification_data.fingerprint;
        const currentFingerprint = fingerprint;
        
        // Compare key device characteristics
        const stored = JSON.parse(atob(storedFingerprint));
        const current = JSON.parse(atob(currentFingerprint));
        
        const criticalMismatches = [];
        
        if (stored.userAgent !== current.userAgent) {
          criticalMismatches.push('userAgent');
        }
        if (stored.platform !== current.platform) {
          criticalMismatches.push('platform');
        }
        if (stored.screen !== current.screen) {
          criticalMismatches.push('screen');
        }

        if (criticalMismatches.length >= 2) {
          return {
            detected: true,
            type: 'device_switching',
            severity: 'critical',
            details: `Critical mismatches: ${criticalMismatches.join(', ')}`,
            confidence: 0.95
          };
        }
      }
    } catch (error) {
      console.error('Device switching detection error:', error);
    }

    return { detected: false };
  }

  // Detect behavioral anomalies
  detectBehaviorAnomaly(sessionData) {
    const { userAgent, referrer, timeSpent, mouseInteractions } = sessionData;
    
    // Check for automation signatures
    const automationSignatures = [
      'selenium', 'webdriver', 'puppeteer', 'playwright', 'chromedriver',
      'headless', 'phantomjs', 'htmlunit', 'bot', 'crawler'
    ];

    const userAgentLower = userAgent.toLowerCase();
    const referrerLower = referrer.toLowerCase();

    for (const signature of automationSignatures) {
      if (userAgentLower.includes(signature) || referrerLower.includes(signature)) {
        return {
          detected: true,
          type: 'automation_signature',
          severity: 'critical',
          details: `Automation signature detected: ${signature}`,
          confidence: 0.98
        };
      }
    }

    // Check for perfect timing patterns (bot-like behavior)
    if (timeSpent > 0 && timeSpent % 1000 === 0 && mouseInteractions === 0) {
      return {
        detected: true,
        type: 'perfect_timing',
        severity: 'medium',
        details: `Perfect timing pattern: ${timeSpent}ms with no interactions`,
        confidence: 0.7
      };
    }

    return { detected: false };
  }

  // Network-based threat detection
  async detectNetworkThreats(sessionData) {
    const { ipHash, userAgent } = sessionData;
    
    // Check for known VPN/proxy patterns
    const vpnIndicators = [
      'vpn', 'proxy', 'tor', 'tunnel', 'anonymous',
      'hide', 'mask', 'private', 'secure'
    ];

    const userAgentLower = userAgent.toLowerCase();
    
    for (const indicator of vpnIndicators) {
      if (userAgentLower.includes(indicator)) {
        return {
          detected: true,
          type: 'network_anonymization',
          severity: 'medium',
          details: `VPN/Proxy indicator detected: ${indicator}`,
          confidence: 0.6
        };
      }
    }

    // Check IP reputation (simplified - in production, use external threat intel)
    const suspiciousActivity = this.suspiciousActivities.get(ipHash) || 0;
    if (suspiciousActivity > 5) {
      return {
        detected: true,
        type: 'ip_reputation',
        severity: 'high',
        details: `IP has ${suspiciousActivity} suspicious activities`,
        confidence: 0.8
      };
    }

    return { detected: false };
  }

  // Calculate overall threat level
  calculateThreatLevel(threats) {
    if (threats.length === 0) return 'none';
    
    const criticalThreats = threats.filter(t => t.severity === 'critical').length;
    const highThreats = threats.filter(t => t.severity === 'high').length;
    const mediumThreats = threats.filter(t => t.severity === 'medium').length;

    if (criticalThreats > 0) return 'critical';
    if (highThreats >= 2) return 'critical';
    if (highThreats >= 1) return 'high';
    if (mediumThreats >= 3) return 'high';
    if (mediumThreats >= 1) return 'medium';
    
    return 'low';
  }

  // Determine action based on threat level
  determineAction(threats) {
    const threatLevel = this.calculateThreatLevel(threats);
    
    switch (threatLevel) {
      case 'critical':
        return {
          action: 'block',
          message: 'Automated tool or bypass service detected. Access denied.',
          logLevel: 'error'
        };
      case 'high':
        return {
          action: 'challenge',
          message: 'Additional verification required.',
          logLevel: 'warn'
        };
      case 'medium':
        return {
          action: 'monitor',
          message: 'Suspicious activity detected. Monitoring session.',
          logLevel: 'info'
        };
      default:
        return {
          action: 'allow',
          message: 'Session appears legitimate.',
          logLevel: 'debug'
        };
    }
  }

  // Log security events
  async logSecurityEvent(event) {
    try {
      await supabase
        .from('security_logs')
        .insert([{
          event_type: event.type,
          severity: event.severity,
          details: event.details,
          ip_hash: event.ipHash,
          session_id: event.sessionId,
          user_agent: event.userAgent,
          created_at: new Date().toISOString()
        }]);
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  // Update IP reputation
  updateIPReputation(ipHash, threatType) {
    const current = this.suspiciousActivities.get(ipHash) || 0;
    this.suspiciousActivities.set(ipHash, current + 1);
  }
}

module.exports = new SecurityMonitor();
