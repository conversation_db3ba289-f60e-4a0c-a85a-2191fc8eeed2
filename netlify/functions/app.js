const crypto = require("crypto");

const RECAPTCHA_SECRET_KEY = process.env.RECAPTCHA_SECRET_KEY;
const JWT_SECRET =
  process.env.JWT_SECRET || "default-secret-change-in-production";
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

function getHeaders() {
  return {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Content-Type": "application/json",
  };
}

function createResponse(statusCode, body) {
  return {
    statusCode,
    headers: getHeaders(),
    body: JSON.stringify(body),
  };
}

function getClientIP(event) {
  const headers = event.headers || {};
  return (
    headers["x-forwarded-for"]?.split(",")[0]?.trim() ||
    headers["x-real-ip"] ||
    headers["cf-connecting-ip"] ||
    event.requestContext?.identity?.sourceIp ||
    "127.0.0.1"
  );
}

function hashIP(ip) {
  return crypto
    .createHash("sha256")
    .update(ip + "project-l-salt")
    .digest("hex");
}

// JWT token functions
function generateSecureToken(payload) {
  const header = Buffer.from(
    JSON.stringify({ alg: "HS256", typ: "JWT" }),
  ).toString("base64url");
  const payloadStr = Buffer.from(JSON.stringify(payload)).toString("base64url");
  const signature = crypto
    .createHmac("sha256", JWT_SECRET)
    .update(`${header}.${payloadStr}`)
    .digest("base64url");
  return `${header}.${payloadStr}.${signature}`;
}

function verifySecureToken(token) {
  try {
    if (!token || typeof token !== "string") {
      return null;
    }

    const parts = token.split(".");
    if (parts.length !== 3) {
      return null;
    }

    const [header, payload, signature] = parts;

    const expectedSignature = crypto
      .createHmac("sha256", JWT_SECRET)
      .update(`${header}.${payload}`)
      .digest("base64url");

    if (signature !== expectedSignature) {
      return null;
    }

    const decodedPayload = JSON.parse(
      Buffer.from(payload, "base64url").toString(),
    );

    if (decodedPayload.exp && decodedPayload.exp < Date.now() / 1000) {
      return null;
    }

    return decodedPayload;
  } catch (error) {
    console.error("Token verification error:", error);
    return null;
  }
}

// reCAPTCHA verification
async function verifyRecaptcha(recaptchaResponse) {
  if (!RECAPTCHA_SECRET_KEY) {
    console.warn(
      "RECAPTCHA_SECRET_KEY not configured, allowing request for development",
    );
    return true;
  }

  if (!recaptchaResponse || recaptchaResponse.length < 10) {
    return false;
  }

  try {
    const response = await fetch(
      "https://www.google.com/recaptcha/api/siteverify",
      {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: `secret=${RECAPTCHA_SECRET_KEY}&response=${recaptchaResponse}`,
      },
    );

    const data = await response.json();
    return data.success === true;
  } catch (error) {
    console.error("reCAPTCHA verification error:", error);
    return false;
  }
}

// Key generation
function generateKey() {
  const segments = [];
  for (let i = 0; i < 3; i++) {
    segments.push(crypto.randomBytes(2).toString("hex").toUpperCase());
  }
  const checksum = crypto
    .createHash("md5")
    .update(segments.join("") + "PROJECT_L_SALT")
    .digest("hex")
    .substring(0, 2)
    .toUpperCase();
  segments.push(checksum);
  return `PL-${segments.join("-")}`;
}

// Database functions with Supabase fallback
let supabase = null;

async function initSupabase() {
  if (supabase) return supabase;

  try {
    if (!SUPABASE_URL || !SUPABASE_KEY) {
      console.warn("Supabase not configured, using memory storage");
      return null;
    }

    const { createClient } = require("@supabase/supabase-js");
    supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
    return supabase;
  } catch (error) {
    console.error("Failed to initialize Supabase:", error);
    return null;
  }
}

// In-memory storage for when Supabase is not available
const memoryStorage = {
  sessions: new Map(),
  keys: new Map(),
  rateLimits: new Map(),
  scripts: new Map(),
};

// Initialize default scripts in memory storage
memoryStorage.scripts.set("main-hub-001", {
  id: 1,
  script_name: "Main Hub Script",
  script_id: "main-hub-001",
  description: "Main script hub with universal compatibility",
  version: "2.1.0",
  script_url: "https://projectmadara.com/scripts/main-hub-001.lua",
  status: "active",
  kill_switch_enabled: true,
  maintenance_message: "Script is currently under maintenance",
  allowed_users: [],
  usage_count: 0,
  metadata: { category: "hub", uploaded: false },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
});

memoryStorage.scripts.set("admin-commands-001", {
  id: 2,
  script_name: "Admin Commands",
  script_id: "admin-commands-001",
  description: "Advanced admin command system",
  version: "3.2.1",
  script_url: "https://projectmadara.com/scripts/admin-commands-001.lua",
  status: "active",
  kill_switch_enabled: true,
  maintenance_message: "Script is currently under maintenance",
  allowed_users: [],
  usage_count: 0,
  metadata: { category: "admin", uploaded: false },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
});

memoryStorage.scripts.set("beta-test-001", {
  id: 3,
  script_name: "Beta Testing Suite",
  script_id: "beta-test-001",
  description: "Experimental features testing environment",
  version: "0.9.2",
  script_url: "https://projectmadara.com/scripts/beta-test-001.lua",
  status: "maintenance",
  kill_switch_enabled: true,
  maintenance_message: "Script is currently under maintenance",
  allowed_users: [],
  usage_count: 0,
  metadata: { category: "beta", uploaded: false },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
});

// Database operations with fallback
const database = {
  async createSession(sessionData) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("user_sessions")
          .insert([
            {
              token: sessionData.token,
              ip_hash: sessionData.ipHash,
              service: sessionData.service || "linkvertise",
              expires_at: sessionData.expiresAt,
              current_step: 1,
              status: "active",
            },
          ])
          .select()
          .single();

        if (error) throw error;
        return data;
      } catch (error) {
        console.error("Supabase session creation failed:", error);
      }
    }

    // Fallback to memory storage
    const session = {
      token: sessionData.token,
      ip_hash: sessionData.ipHash,
      service: sessionData.service || "linkvertise",
      expires_at: sessionData.expiresAt,
      current_step: 1,
      status: "active",
      created_at: new Date().toISOString(),
    };
    memoryStorage.sessions.set(sessionData.token, session);
    return session;
  },

  async getSession(token) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("user_sessions")
          .select("*")
          .eq("token", token)
          .eq("status", "active")
          .single();

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase session fetch failed:", error);
      }
    }

    // Fallback to memory storage
    const session = memoryStorage.sessions.get(token);
    if (session && new Date(session.expires_at) > new Date()) {
      return session;
    }
    return null;
  },

  async updateSession(token, updates) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("user_sessions")
          .update(updates)
          .eq("token", token)
          .select()
          .single();

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase session update failed:", error);
      }
    }

    // Fallback to memory storage
    const session = memoryStorage.sessions.get(token);
    if (session) {
      Object.assign(session, updates);
      memoryStorage.sessions.set(token, session);
      return session;
    }
    return null;
  },

  async createKey(keyData) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("keys")
          .insert([
            {
              key_code: keyData.key,
              ip_hash: keyData.ipHash,
              session_id: keyData.sessionId,
              created_by: keyData.createdBy || "user",
              expires_at: keyData.expiresAt,
              metadata: keyData.metadata || {},
              status: "active",
            },
          ])
          .select()
          .single();

        if (error) throw error;
        console.log("Key saved to database:", data.key_code);
        return data;
      } catch (error) {
        console.error("Supabase key creation failed:", error);
      }
    }

    // Fallback to memory storage
    const key = {
      key_code: keyData.key,
      ip_hash: keyData.ipHash,
      session_id: keyData.sessionId,
      created_by: keyData.createdBy || "user",
      expires_at: keyData.expiresAt,
      metadata: keyData.metadata || {},
      status: "active",
      created_at: new Date().toISOString(),
      usage_count: 0,
    };
    memoryStorage.keys.set(keyData.key, key);
    console.log("Key saved to memory storage:", keyData.key);
    return key;
  },

  async hasGeneratedKeyToday(ipHash) {
    const client = await initSupabase();

    if (client) {
      try {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const { data, error } = await client
          .from("keys")
          .select("id")
          .eq("ip_hash", ipHash)
          .gte("created_at", today.toISOString())
          .limit(1);

        if (!error && data) {
          return data.length > 0;
        }
      } catch (error) {
        console.error("Supabase daily check failed:", error);
      }
    }

    // Fallback to memory storage
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (const [keyCode, keyData] of memoryStorage.keys.entries()) {
      if (keyData.ip_hash === ipHash && new Date(keyData.created_at) >= today) {
        return true;
      }
    }
    return false;
  },

  async getKey(keyCode) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("keys")
          .select("*")
          .eq("key_code", keyCode)
          .single();

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase key fetch failed:", error);
      }
    }

    // Fallback to memory storage
    return memoryStorage.keys.get(keyCode);
  },

  async updateKey(keyCode, updates) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("keys")
          .update(updates)
          .eq("key_code", keyCode)
          .select()
          .single();

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase key update failed:", error);
      }
    }

    // Fallback to memory storage
    const key = memoryStorage.keys.get(keyCode);
    if (key) {
      Object.assign(key, updates);
      memoryStorage.keys.set(keyCode, key);
      return key;
    }
    return null;
  },

  async validateAndUseKey(key, hwid) {
    try {
      const keyData = await this.getKey(key);

      if (!keyData) {
        return { valid: false, reason: "Key not found" };
      }

      // Check expiration
      if (new Date(keyData.expires_at) < new Date()) {
        return { valid: false, reason: "Key has expired" };
      }

      // Check status
      if (keyData.status !== "active") {
        return { valid: false, reason: "Key is not active" };
      }

      // HWID binding logic
      if (!keyData.hwid) {
        // First time use - bind to HWID
        await this.updateKey(key, {
          hwid: hwid,
          last_used_at: new Date().toISOString(),
          usage_count: (keyData.usage_count || 0) + 1,
        });
        return { valid: true, reason: "Key validated and bound to device" };
      } else if (keyData.hwid === hwid) {
        // HWID matches - update usage
        await this.updateKey(key, {
          last_used_at: new Date().toISOString(),
          usage_count: (keyData.usage_count || 0) + 1,
        });
        return { valid: true, reason: "Key validated successfully" };
      } else {
        // HWID mismatch
        return { valid: false, reason: "Key is bound to a different device" };
      }
    } catch (error) {
      console.error("Key validation error:", error);
      return { valid: false, reason: "Validation error" };
    }
  },

  async deleteSession(token) {
    const client = await initSupabase();

    if (client) {
      try {
        await client.from("user_sessions").delete().eq("token", token);
      } catch (error) {
        console.error("Supabase session deletion failed:", error);
      }
    }

    memoryStorage.sessions.delete(token);
  },

  // Script management functions
  async getAllScripts() {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("scripts")
          .select("*")
          .order("created_at", { ascending: false });

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase scripts fetch failed:", error);
      }
    }

    // Fallback to memory storage
    return Array.from(memoryStorage.scripts?.values() || []);
  },

  async getScript(scriptId) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("scripts")
          .select("*")
          .eq("script_id", scriptId)
          .single();

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase script fetch failed:", error);
      }
    }

    // Fallback to memory storage
    if (!memoryStorage.scripts) memoryStorage.scripts = new Map();
    return memoryStorage.scripts.get(scriptId);
  },

  async createScript(scriptData) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("scripts")
          .insert([
            {
              script_name: scriptData.name,
              script_id: scriptData.id,
              description: scriptData.description || "",
              version: scriptData.version || "1.0.0",
              script_url: scriptData.url,
              status: scriptData.status || "active",
              kill_switch_enabled: scriptData.killSwitchEnabled !== false,
              maintenance_message:
                scriptData.maintenanceMessage ||
                "Script is currently under maintenance",
              allowed_users: scriptData.allowedUsers || [],
              metadata: scriptData.metadata || {},
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          ])
          .select()
          .single();

        if (error) throw error;
        return data;
      } catch (error) {
        console.error("Supabase script creation failed:", error);
      }
    }

    // Fallback to memory storage
    if (!memoryStorage.scripts) memoryStorage.scripts = new Map();
    const script = {
      id: Date.now(),
      script_name: scriptData.name,
      script_id: scriptData.id,
      description: scriptData.description || "",
      version: scriptData.version || "1.0.0",
      script_url: scriptData.url,
      status: scriptData.status || "active",
      kill_switch_enabled: scriptData.killSwitchEnabled !== false,
      maintenance_message:
        scriptData.maintenanceMessage ||
        "Script is currently under maintenance",
      allowed_users: scriptData.allowedUsers || [],
      usage_count: 0,
      metadata: scriptData.metadata || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    memoryStorage.scripts.set(scriptData.id, script);
    return script;
  },

  async updateScriptStatus(scriptId, updates) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("scripts")
          .update({
            ...updates,
            updated_at: new Date().toISOString(),
          })
          .eq("script_id", scriptId)
          .select()
          .single();

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase script update failed:", error);
      }
    }

    // Fallback to memory storage
    if (!memoryStorage.scripts) memoryStorage.scripts = new Map();
    const script = memoryStorage.scripts.get(scriptId);
    if (script) {
      Object.assign(script, updates, { updated_at: new Date().toISOString() });
      memoryStorage.scripts.set(scriptId, script);
      return script;
    }
    return null;
  },

  async deleteScript(scriptId) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("scripts")
          .delete()
          .eq("script_id", scriptId)
          .select()
          .single();

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase script deletion failed:", error);
      }
    }

    // Fallback to memory storage
    if (!memoryStorage.scripts) memoryStorage.scripts = new Map();
    const script = memoryStorage.scripts.get(scriptId);
    if (script) {
      memoryStorage.scripts.delete(scriptId);
      return script;
    }
    return null;
  },

  async logScriptUsage(
    scriptId,
    userHwid,
    userKey,
    action,
    status,
    errorMessage = null,
  ) {
    const client = await initSupabase();

    if (client) {
      try {
        await client.from("script_usage").insert([
          {
            script_id: scriptId,
            user_hwid: userHwid,
            user_key: userKey,
            action: action,
            status: status,
            error_message: errorMessage,
            metadata: {},
            created_at: new Date().toISOString(),
          },
        ]);
      } catch (error) {
        console.error("Supabase script usage logging failed:", error);
      }
    }

    // For memory storage, we'll just log to console
    console.log(
      `Script Usage: ${scriptId} - ${action} - ${status}${errorMessage ? ` - ${errorMessage}` : ""}`,
    );
  },

  async getScriptUsageLogs(scriptId, limit = 100) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("script_usage")
          .select("*")
          .eq("script_id", scriptId)
          .order("created_at", { ascending: false })
          .limit(limit);

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase script usage logs fetch failed:", error);
      }
    }

    // Fallback to empty array for memory storage
    return [];
  },

  async getScriptUsageStats(scriptId) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("script_usage")
          .select("action, status, created_at")
          .eq("script_id", scriptId);

        if (!error && data) {
          // Process the data into analytics format
          const analytics = {
            total_usage: data.length,
            successful_starts: data.filter(
              (d) => d.action === "start" && d.status === "success",
            ).length,
            blocked_attempts: data.filter((d) => d.status === "blocked").length,
            errors: data.filter((d) => d.status === "error").length,
            daily_usage: {},
          };

          // Group by day
          data.forEach((log) => {
            const date = new Date(log.created_at).toISOString().split("T")[0];
            if (!analytics.daily_usage[date]) {
              analytics.daily_usage[date] = 0;
            }
            analytics.daily_usage[date]++;
          });

          return analytics;
        }
      } catch (error) {
        console.error("Supabase script analytics fetch failed:", error);
      }
    }

    // Fallback to empty stats for memory storage
    return {
      total_usage: 0,
      successful_starts: 0,
      blocked_attempts: 0,
      errors: 0,
      daily_usage: {},
    };
  },

  async getSystemSettings() {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("system_settings")
          .select("*");

        if (!error && data) {
          // Return in array format to match frontend expectations
          return data;
        }
      } catch (error) {
        console.error("Supabase system settings fetch failed:", error);
      }
    }

    // Fallback to default settings in array format
    return [
      { setting_key: "global_kill_switch", setting_value: "false" },
      { setting_key: "maintenance_mode", setting_value: "false" },
      {
        setting_key: "maintenance_message",
        setting_value: '"System is under maintenance. Please try again later."',
      },
      { setting_key: "heartbeat_interval", setting_value: "30" },
    ];
  },

  async getSystemSetting(key) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("system_settings")
          .select("setting_value")
          .eq("setting_key", key)
          .single();

        if (!error && data) return data.setting_value;
      } catch (error) {
        console.error("Supabase system setting fetch failed:", error);
      }
    }

    // Fallback to defaults
    const defaults = {
      global_kill_switch: "false",
      maintenance_mode: "false",
      maintenance_message:
        '"System is under maintenance. Please try again later."',
      heartbeat_interval: "30",
    };

    return defaults[key] || null;
  },

  async updateSystemSetting(key, value) {
    const client = await initSupabase();

    if (client) {
      try {
        const { data, error } = await client
          .from("system_settings")
          .upsert({
            setting_key: key,
            setting_value: value,
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (!error && data) return data;
      } catch (error) {
        console.error("Supabase system setting update failed:", error);
      }
    }

    // For memory storage, just return success
    console.log(`System setting updated: ${key} = ${value}`);
    return { setting_key: key, setting_value: value };
  },
};

// Admin authentication
function verifyAdminToken(authHeader) {
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.split(" ")[1];

  // Try JWT verification first (from admin.js)
  try {
    const jwt = require("jsonwebtoken");
    const ADMIN_JWT_SECRET =
      process.env.ADMIN_JWT_SECRET || "your_admin_jwt_secret_here";
    const decoded = jwt.verify(token, ADMIN_JWT_SECRET);

    // Check if token has admin role
    if (decoded.role === "admin" || decoded.role === "owner") {
      return decoded;
    }
  } catch (jwtError) {
    console.log(
      "JWT verification failed, trying custom token:",
      jwtError.message,
    );
  }

  // Fallback to custom token verification
  return verifySecureToken(token);
}

// Rate limiting
const rateLimits = new Map();

function checkRateLimit(identifier, maxRequests = 10, windowMs = 60000) {
  const now = Date.now();
  const windowStart = now - windowMs;

  // Clean old entries
  for (const [key, timestamps] of rateLimits.entries()) {
    const validTimestamps = timestamps.filter((ts) => ts > windowStart);
    if (validTimestamps.length === 0) {
      rateLimits.delete(key);
    } else {
      rateLimits.set(key, validTimestamps);
    }
  }

  // Check current requests
  const requests = rateLimits.get(identifier) || [];
  const recentRequests = requests.filter((ts) => ts > windowStart);

  if (recentRequests.length >= maxRequests) {
    return false;
  }

  recentRequests.push(now);
  rateLimits.set(identifier, recentRequests);
  return true;
}

// Main handler
exports.handler = async (event, context) => {
  const path = event.path.replace("/.netlify/functions/app", "");
  const method = event.httpMethod;

  // Handle CORS
  if (method === "OPTIONS") {
    return createResponse(200, { message: "CORS preflight successful" });
  }

  try {
    console.log(`${method} ${path}`, event.body ? "with body" : "no body");

    const clientIP = getClientIP(event);
    const identifier = hashIP(clientIP);

    if (!checkRateLimit(identifier, 30, 60000)) {
      return createResponse(429, {
        error: "Too many requests. Please slow down.",
      });
    }

    if (path === "/generate-token" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { recaptchaResponse } = body;

        const isValid = await verifyRecaptcha(recaptchaResponse);
        if (!isValid) {
          return createResponse(400, { error: "Invalid captcha verification" });
        }

        const tokenId = crypto.randomBytes(16).toString("hex");
        const expiresAt = new Date(Date.now() + 30 * 60 * 1000);

        const payload = {
          tokenId,
          current_step: 1,
          created_at: new Date().toISOString(),
          expires_at: expiresAt.toISOString(),
          exp: Math.floor(expiresAt.getTime() / 1000),
        };

        const token = generateSecureToken(payload);

        const ipHash = hashIP(clientIP);
        await database.createSession({
          token: tokenId,
          ipHash: ipHash,
          expiresAt: expiresAt.toISOString(),
          service: "linkvertise",
        });

        return createResponse(200, { token });
      } catch (error) {
        console.error("Generate token error:", error);
        return createResponse(500, { error: "Failed to generate token" });
      }
    }

    if (path === "/verify-step" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { token, step, recaptchaResponse, verificationData } = body;

        const isValid = await verifyRecaptcha(recaptchaResponse);
        if (!isValid) {
          return createResponse(400, { error: "Invalid captcha verification" });
        }

        const decodedToken = verifySecureToken(token);
        if (!decodedToken) {
          return createResponse(400, { error: "Invalid or expired token" });
        }

        const tokenData = await database.getSession(decodedToken.tokenId);
        if (!tokenData) {
          return createResponse(400, { error: "Session not found or expired" });
        }

        if (new Date(tokenData.expires_at) < new Date()) {
          return createResponse(400, { error: "Session has expired" });
        }

        const nextStep = Math.max(tokenData.current_step, step + 1);
        await database.updateSession(decodedToken.tokenId, {
          current_step: nextStep,
          verification_data: verificationData || {},
          ip_hash: hashIP(clientIP),
        });

        return createResponse(200, { verified: true });
      } catch (error) {
        console.error("Verify step error:", error);
        return createResponse(500, { error: "Failed to verify step" });
      }
    }

    if (path === "/track-link-visit" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { token, service } = body;

        const decodedToken = verifySecureToken(token);
        if (!decodedToken) {
          return createResponse(400, { error: "Invalid token" });
        }

        await database.updateSession(decodedToken.tokenId, {
          service: service,
          link_visit_time: new Date().toISOString(),
        });

        return createResponse(200, { message: "Visit tracked" });
      } catch (error) {
        console.error("Track link visit error:", error);
        return createResponse(500, { error: "Failed to track visit" });
      }
    }

    if (path === "/generate-key" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { token, recaptchaResponse, skipRecaptcha } = body;

        if (!skipRecaptcha) {
          const isValid = await verifyRecaptcha(recaptchaResponse);
          if (!isValid) {
            return createResponse(400, {
              error: "Invalid captcha verification",
            });
          }
        }

        const decodedToken = verifySecureToken(token);
        if (!decodedToken) {
          return createResponse(400, { error: "Invalid or expired token" });
        }

        const tokenData = await database.getSession(decodedToken.tokenId);
        if (!tokenData) {
          return createResponse(400, { error: "Session not found" });
        }

        if (tokenData.current_step < 3) {
          return createResponse(400, {
            error: "Please complete all verification steps first",
          });
        }

        const ipHash = hashIP(clientIP);

        const hasGeneratedToday = await database.hasGeneratedKeyToday(ipHash);
        if (hasGeneratedToday) {
          return createResponse(400, {
            error:
              "You have already generated a key today. Please try again tomorrow.",
          });
        }

        const key = generateKey();
        const expiresAt = new Date(
          Date.now() + 24 * 60 * 60 * 1000,
        ).toISOString();

        await database.createKey({
          key: key,
          sessionId: decodedToken.tokenId,
          ipHash: ipHash,
          expiresAt: expiresAt,
          createdBy: "user",
          metadata: {
            sessionToken: decodedToken.tokenId,
            userAgent: event.headers["user-agent"] || "",
            generationMethod: "web_interface",
          },
        });

        await database.deleteSession(decodedToken.tokenId);

        return createResponse(200, {
          key,
          expires_at: expiresAt,
          message: "Key generated successfully!",
        });
      } catch (error) {
        console.error("Generate key error:", error);
        return createResponse(500, { error: "Failed to generate key" });
      }
    }

    if (path === "/validate-key" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { key, hwid } = body;

        if (!key || !hwid) {
          return createResponse(400, { error: "Missing key or HWID" });
        }

        const validation = await database.validateAndUseKey(key, hwid);

        if (!validation.valid) {
          return createResponse(400, {
            valid: false,
            error: validation.reason,
          });
        }

        return createResponse(200, {
          valid: true,
          message: validation.reason,
        });
      } catch (error) {
        console.error("Validate key error:", error);
        return createResponse(500, { error: "Failed to validate key" });
      }
    }

    if (path === "/reset-hwid" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { key } = body;

        if (!key) {
          return createResponse(400, { error: "Key is required" });
        }

        const keyData = await database.getKey(key);
        if (!keyData) {
          return createResponse(404, { error: "Key not found" });
        }

        if (new Date(keyData.expires_at) < new Date()) {
          return createResponse(400, {
            error: "Cannot reset HWID for expired key",
          });
        }

        await database.updateKey(key, { hwid: null });

        return createResponse(200, {
          message:
            "HWID reset successfully. You can now use this key on a different device.",
        });
      } catch (error) {
        console.error("Reset HWID error:", error);
        return createResponse(500, { error: "Failed to reset HWID" });
      }
    }

    // Handle Linkvertise token verification
    if (path === "/verify-linkvertise-token" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { token, progressionToken } = body;

        if (!token) {
          return createResponse(400, {
            error: "Linkvertise token is required",
          });
        }

        if (!progressionToken) {
          return createResponse(400, {
            error: "Progression token is required",
          });
        }

        // Verify our progression token
        const decodedToken = verifySecureToken(progressionToken);
        if (!decodedToken) {
          return createResponse(400, {
            error: "Invalid or expired progression token",
          });
        }

        const tokenId = decodedToken.tokenId;
        const tokenData = await database.getSession(tokenId);

        if (!tokenData) {
          return createResponse(400, { error: "Invalid token data" });
        }

        // For now, we'll consider any Linkvertise token valid
        // In a production system, you would validate with the Linkvertise API

        // Update token to indicate Linkvertise verification
        const ipHash = hashIP(clientIP);

        const updateData = {
          lv_token_verified: true,
          ip_hash: ipHash,
          verification_data: JSON.stringify({
            linkvertise_verified: true,
            linkvertise_verification_time: new Date().toISOString(),
            linkvertise_token: token.substring(0, 10) + "...", // Only store part of the token for security
          }),
        };

        // Mark the token as verified in our system
        await database.updateSession(tokenId, updateData);

        // Create a HMAC of the token for verification on client
        const tokenHmac = crypto
          .createHmac("sha256", JWT_SECRET)
          .update(token)
          .digest("hex");

        return createResponse(200, {
          verified: true,
          message: "Linkvertise token verified successfully",
          verification_id: tokenHmac,
        });
      } catch (error) {
        console.error("Linkvertise token verification error:", error);
        return createResponse(500, {
          error: "Server error verifying Linkvertise token",
        });
      }
    }

    if (path === "/can-generate-key" && method === "GET") {
      try {
        const ipHash = hashIP(clientIP);
        const hasGenerated = await database.hasGeneratedKeyToday(ipHash);

        return createResponse(200, {
          canGenerate: !hasGenerated,
          message: hasGenerated
            ? "You have already generated a key today"
            : "You can generate a new key today",
        });
      } catch (error) {
        console.error("Can generate key error:", error);
        return createResponse(500, {
          error: "Failed to check generation status",
        });
      }
    }

    if (path === "/get-todays-key" && method === "GET") {
      try {
        const ipHash = hashIP(clientIP);

        return createResponse(404, {
          error: "No key found for today",
          message: "You have not generated a key today yet.",
        });
      } catch (error) {
        console.error("Get todays key error:", error);
        return createResponse(500, { error: "Failed to get today's key" });
      }
    }

    // Simple key check for Lua scripts
    if (method === "GET" && event.queryStringParameters) {
      const { action, key, hwid } = event.queryStringParameters;

      if (action === "check-key" && key && hwid) {
        try {
          const validation = await database.validateAndUseKey(key, hwid);

          return createResponse(200, {
            valid: validation.valid,
            message: validation.reason,
          });
        } catch (error) {
          console.error("Simple key check error:", error);
          return createResponse(500, { error: "Validation failed" });
        }
      }
    }

    if (path === "/debug" && method === "GET") {
      return createResponse(200, {
        environment: {
          hasRecaptchaSecret: !!RECAPTCHA_SECRET_KEY,
          hasJwtSecret: !!JWT_SECRET,
          hasSupabase: !!SUPABASE_URL && !!SUPABASE_KEY,
          nodeVersion: process.version,
        },
        timestamp: new Date().toISOString(),
        memoryUsage: process.memoryUsage(),
      });
    }

    // Kill Switch API Endpoints

    // Check script status - for Lua scripts
    if (method === "GET" && event.queryStringParameters) {
      const { action, script_id, hwid, key } = event.queryStringParameters;

      if (action === "check-script" && script_id) {
        try {
          // Check global kill switch first
          const globalKillSwitch =
            await database.getSystemSetting("global_kill_switch");
          if (globalKillSwitch === "true") {
            await database.logScriptUsage(
              script_id,
              hwid,
              key,
              "blocked",
              "Global kill switch enabled",
            );
            return createResponse(200, {
              allowed: false,
              reason:
                "System is currently under maintenance. Please try again later.",
              status: "blocked",
            });
          }

          // Check maintenance mode
          const maintenanceMode =
            await database.getSystemSetting("maintenance_mode");
          if (maintenanceMode === "true") {
            const maintenanceMessage = await database.getSystemSetting(
              "maintenance_message",
            );
            await database.logScriptUsage(
              script_id,
              hwid,
              key,
              "blocked",
              "Maintenance mode enabled",
            );
            return createResponse(200, {
              allowed: false,
              reason: maintenanceMessage || "System is under maintenance.",
              status: "maintenance",
            });
          }

          // Check specific script status
          const script = await database.getScript(script_id);
          if (!script) {
            await database.logScriptUsage(
              script_id,
              hwid,
              key,
              "error",
              "Script not found",
            );
            return createResponse(200, {
              allowed: false,
              reason: "Script not found.",
              status: "not_found",
            });
          }

          if (script.status === "disabled") {
            await database.logScriptUsage(
              script_id,
              hwid,
              key,
              "blocked",
              "Script disabled",
            );
            return createResponse(200, {
              allowed: false,
              reason: "This script has been disabled.",
              status: "disabled",
            });
          }

          if (script.status === "maintenance") {
            // Check if user is in allowed list
            if (script.allowed_users && script.allowed_users.includes(hwid)) {
              await database.logScriptUsage(
                script_id,
                hwid,
                key,
                "success",
                "Maintenance bypass allowed",
              );
              return createResponse(200, {
                allowed: true,
                reason: "Access granted during maintenance.",
                status: "maintenance_bypass",
              });
            }

            await database.logScriptUsage(
              script_id,
              hwid,
              key,
              "blocked",
              "Script in maintenance",
            );
            return createResponse(200, {
              allowed: false,
              reason:
                script.maintenance_message ||
                "Script is currently under maintenance.",
              status: "maintenance",
            });
          }

          // Script is active - allow execution
          await database.updateScriptUsage(script_id);
          await database.logScriptUsage(
            script_id,
            hwid,
            key,
            "success",
            "Script execution allowed",
          );

          return createResponse(200, {
            allowed: true,
            reason: "Script execution authorized.",
            status: "active",
            heartbeat_interval:
              (await database.getSystemSetting("heartbeat_interval")) || "30",
          });
        } catch (error) {
          console.error("Script check error:", error);
          return createResponse(200, {
            allowed: false,
            reason: "Unable to verify script status. Please try again.",
            status: "error",
          });
        }
      }
    }

    // Script heartbeat endpoint
    if (path === "/script-heartbeat" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { script_id, hwid, key, session_token } = body;

        if (!script_id || !hwid) {
          return createResponse(400, { error: "Missing required parameters" });
        }

        // Validate the session is still active
        const script = await database.getScript(script_id);
        if (!script || script.status !== "active") {
          return createResponse(200, {
            continue: false,
            reason: "Script has been disabled or is under maintenance.",
          });
        }

        // Check global settings
        const globalKillSwitch =
          await database.getSystemSetting("global_kill_switch");
        if (globalKillSwitch === "true") {
          return createResponse(200, {
            continue: false,
            reason: "System maintenance in progress.",
          });
        }

        // Log heartbeat
        await database.logScriptUsage(
          script_id,
          hwid,
          key,
          "heartbeat",
          "Active session",
        );

        return createResponse(200, {
          continue: true,
          next_check: parseInt(
            (await database.getSystemSetting("heartbeat_interval")) || "30",
          ),
        });
      } catch (error) {
        console.error("Heartbeat error:", error);
        return createResponse(200, {
          continue: false,
          reason: "Unable to verify session status.",
        });
      }
    }

    // Get script list for admin
    // Get all scripts for admin
    if (path === "/admin/scripts" && method === "GET") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const scripts = await database.getAllScripts();
        return createResponse(200, { scripts });
      } catch (error) {
        console.error("Get scripts error:", error);
        if (
          error.message.includes("does not exist") ||
          error.message.includes("relation")
        ) {
          return createResponse(200, {
            scripts: [],
            database_setup_required: true,
          });
        }
        return createResponse(500, { error: "Failed to get scripts" });
      }
    }

    // Update script status for admin
    if (path === "/admin/scripts/update" && method === "POST") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const body = JSON.parse(event.body || "{}");
        const { script_id, status, maintenance_message, allowed_users } = body;

        if (!script_id || !status) {
          return createResponse(400, { error: "Missing required parameters" });
        }

        await database.updateScriptStatus(script_id, {
          status,
          maintenance_message,
          allowed_users,
        });

        return createResponse(200, { message: "Script updated successfully" });
      } catch (error) {
        console.error("Update script error:", error);
        return createResponse(500, { error: "Failed to update script" });
      }
    }

    // Get system settings for admin
    if (path === "/admin/system" && method === "GET") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const settings = await database.getSystemSettings();
        return createResponse(200, { settings });
      } catch (error) {
        console.error("Get system settings error:", error);
        if (
          error.message.includes("does not exist") ||
          error.message.includes("relation")
        ) {
          return createResponse(200, {
            settings: [
              { setting_key: "global_kill_switch", setting_value: "false" },
              { setting_key: "maintenance_mode", setting_value: "false" },
              {
                setting_key: "maintenance_message",
                setting_value:
                  '"System is under maintenance. Please try again later."',
              },
              { setting_key: "heartbeat_interval", setting_value: "30" },
            ],
            database_setup_required: true,
          });
        }
        return createResponse(500, { error: "Failed to get system settings" });
      }
    }

    // Update system settings for admin
    if (path === "/admin/system" && method === "POST") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const body = JSON.parse(event.body || "{}");
        const { setting_key, setting_value } = body;

        if (!setting_key) {
          return createResponse(400, { error: "Missing setting_key" });
        }

        await database.updateSystemSetting(setting_key, setting_value);
        return createResponse(200, {
          message: "System setting updated successfully",
        });
      } catch (error) {
        console.error("Update system setting error:", error);
        return createResponse(500, {
          error: "Failed to update system setting",
        });
      }
    }

    // Bulk operations endpoint for admin
    // Bulk script operations for admin
    if (path === "/admin/scripts/bulk" && method === "POST") {
      try {
        console.log("Bulk endpoint hit, checking auth...");
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          console.log("Bulk auth failed");
          return createResponse(401, { error: "Unauthorized" });
        }

        if (!event.body) {
          return createResponse(400, { error: "No request body provided" });
        }

        let body;
        try {
          body = JSON.parse(event.body);
        } catch (parseError) {
          console.log("Bulk JSON parse error:", parseError);
          return createResponse(400, { error: "Invalid JSON in request body" });
        }

        const { operation, script_ids } = body;
        console.log("Bulk operation:", { operation, script_ids });

        if (!operation) {
          return createResponse(400, { error: "Missing operation" });
        }
        if (!Array.isArray(script_ids)) {
          return createResponse(400, { error: "script_ids must be an array" });
        }

        const results = [];

        for (const script_id of script_ids) {
          try {
            switch (operation) {
              case "enable":
                await database.updateScriptStatus(script_id, {
                  status: "active",
                });
                results.push({ script_id, success: true });
                break;
              case "disable":
                await database.updateScriptStatus(script_id, {
                  status: "disabled",
                });
                results.push({ script_id, success: true });
                break;
              case "maintenance":
                await database.updateScriptStatus(script_id, {
                  status: "maintenance",
                });
                results.push({ script_id, success: true });
                break;
              case "delete":
                await database.deleteScript(script_id);
                results.push({ script_id, success: true });
                break;
              default:
                results.push({
                  script_id,
                  success: false,
                  error: "Invalid operation",
                });
            }
          } catch (error) {
            results.push({ script_id, success: false, error: error.message });
          }
        }

        return createResponse(200, { results });
      } catch (error) {
        console.error("Bulk operations error:", error);
        return createResponse(500, {
          error: "Failed to execute bulk operation",
        });
      }
    }

    // Create new script for admin
    if (path === "/admin/scripts/create" && method === "POST") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const body = JSON.parse(event.body || "{}");
        const { script_name, script_id, description, version, script_url } =
          body;

        if (!script_name || !script_id) {
          return createResponse(400, {
            error: "Missing required fields: script_name and script_id",
          });
        }

        // Check if script ID already exists
        const existingScript = await database.getScript(script_id);
        if (existingScript) {
          return createResponse(400, { error: "Script ID already exists" });
        }

        const script = await database.createScript({
          name: script_name,
          id: script_id,
          description: description || "",
          version: version || "1.0.0",
          url: script_url || `/.netlify/functions/app/scripts/${script_id}.lua`,
          status: "active",
          killSwitchEnabled: true,
          maintenanceMessage: "Script is currently under maintenance",
          allowedUsers: [],
          metadata: {
            uploaded: false,
          },
        });

        return createResponse(200, {
          message: "Script created successfully",
          script: script,
        });
      } catch (error) {
        console.error("Create script error:", error);
        return createResponse(500, { error: "Failed to create script" });
      }
    }

    // Delete script for admin
    if (path === "/admin/scripts/delete" && method === "POST") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const body = JSON.parse(event.body || "{}");
        const { script_id } = body;

        if (!script_id) {
          return createResponse(400, { error: "Missing script_id" });
        }

        await database.deleteScript(script_id);
        return createResponse(200, { message: "Script deleted successfully" });
      } catch (error) {
        console.error("Delete script error:", error);
        return createResponse(500, { error: "Failed to delete script" });
      }
    }

    // Get script usage logs for admin
    if (path === "/admin/scripts/logs" && method === "GET") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const { script_id, limit } = event.queryStringParameters || {};

        if (!script_id) {
          return createResponse(400, { error: "Missing script_id parameter" });
        }

        const logs = await database.getScriptUsageLogs(
          script_id,
          parseInt(limit) || 100,
        );
        return createResponse(200, { logs });
      } catch (error) {
        console.error("Get script logs error:", error);
        return createResponse(500, { error: "Failed to get script logs" });
      }
    }

    // Get active script sessions for admin
    if (path === "/admin/scripts/sessions" && method === "GET") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const { script_id } = event.queryStringParameters || {};

        if (!script_id) {
          return createResponse(400, { error: "Missing script_id parameter" });
        }

        // This would get active sessions for the script
        // For now, return empty array
        return createResponse(200, { sessions: [] });
      } catch (error) {
        console.error("Get script sessions error:", error);
        return createResponse(500, { error: "Failed to get script sessions" });
      }
    }

    // Terminate script session for admin
    if (path === "/admin/scripts/terminate-session" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { session_token } = body;

        if (!session_token) {
          return createResponse(400, { error: "Missing session_token" });
        }

        await database.terminateScriptSession(session_token);
        return createResponse(200, {
          message: "Session terminated successfully",
        });
      } catch (error) {
        console.error("Terminate session error:", error);
        return createResponse(500, { error: "Failed to terminate session" });
      }
    }

    // Get script analytics for admin
    if (path === "/admin/scripts/analytics" && method === "GET") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const { script_id, period } = event.queryStringParameters || {};

        if (!script_id) {
          return createResponse(400, { error: "Missing script_id parameter" });
        }

        const analytics = await database.getScriptUsageStats(script_id);
        return createResponse(200, { analytics });
      } catch (error) {
        console.error("Get script analytics error:", error);
        return createResponse(500, { error: "Failed to get script analytics" });
      }
    }

    // Bulk script operations for admin
    if (path === "/admin/scripts/bulk" && method === "POST") {
      try {
        const body = JSON.parse(event.body || "{}");
        const { action, script_ids } = body;

        if (!action || !script_ids || !Array.isArray(script_ids)) {
          return createResponse(400, {
            error: "Missing action or script_ids array",
          });
        }

        let results = [];
        for (const script_id of script_ids) {
          try {
            switch (action) {
              case "enable":
                await database.updateScriptStatus(script_id, {
                  status: "active",
                });
                results.push({ script_id, success: true });
                break;
              case "disable":
                await database.updateScriptStatus(script_id, {
                  status: "disabled",
                });
                results.push({ script_id, success: true });
                break;
              case "maintenance":
                await database.updateScriptStatus(script_id, {
                  status: "maintenance",
                });
                results.push({ script_id, success: true });
                break;
              default:
                results.push({
                  script_id,
                  success: false,
                  error: "Invalid action",
                });
            }
          } catch (error) {
            results.push({ script_id, success: false, error: error.message });
          }
        }

        return createResponse(200, {
          message: `Bulk ${action} operation completed`,
          results,
        });
      } catch (error) {
        console.error("Bulk script operation error:", error);
        return createResponse(500, {
          error: "Failed to perform bulk operation",
        });
      }
    }

    // Upload script file for admin
    if (path === "/admin/scripts/upload" && method === "POST") {
      try {
        console.log("Upload endpoint hit, checking auth...");
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          console.log("Upload auth failed");
          return createResponse(401, { error: "Unauthorized" });
        }

        console.log("Upload auth passed, parsing body...");
        if (!event.body) {
          return createResponse(400, { error: "No request body provided" });
        }

        let body;
        try {
          body = JSON.parse(event.body);
        } catch (parseError) {
          console.log("JSON parse error:", parseError);
          return createResponse(400, { error: "Invalid JSON in request body" });
        }

        const { script_name, script_id, script_content, description, version } =
          body;
        console.log("Upload data:", {
          script_name,
          script_id,
          has_content: !!script_content,
        });

        if (!script_name) {
          return createResponse(400, { error: "Missing script_name" });
        }
        if (!script_id) {
          return createResponse(400, { error: "Missing script_id" });
        }
        if (!script_content) {
          return createResponse(400, { error: "Missing script_content" });
        }

        // Check if script ID already exists
        const existingScript = await database.getScript(script_id);
        if (existingScript) {
          return createResponse(400, { error: "Script ID already exists" });
        }

        // Create script URL (this would be the hosted path)
        const script_url = `/.netlify/functions/app/scripts/${script_id}.lua`;

        // Create script in database
        const script = await database.createScript({
          name: script_name,
          id: script_id,
          description: description || "",
          version: version || "1.0.0",
          url: script_url,
          status: "active",
          killSwitchEnabled: true,
          maintenanceMessage: "Script is currently under maintenance",
          allowedUsers: [],
          metadata: {
            uploaded: true,
            file_size: script_content.length,
            upload_date: new Date().toISOString(),
            script_content: script_content,
          },
        });

        console.log("Script created successfully:", script_id);

        return createResponse(200, {
          message: "Script uploaded and created successfully",
          script: script,
          script_url: script_url,
        });
      } catch (error) {
        console.error("Upload script error:", error);
        return createResponse(500, {
          error: "Failed to upload script: " + error.message,
        });
      }
    }

    // Get script content for admin
    if (path === "/admin/scripts/content" && method === "GET") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const { script_id } = event.queryStringParameters || {};

        if (!script_id) {
          return createResponse(400, { error: "Missing script_id parameter" });
        }

        const script = await database.getScript(script_id);
        if (!script) {
          return createResponse(404, { error: "Script not found" });
        }

        const content = script.metadata?.script_content || "";
        return createResponse(200, {
          script_id,
          content,
          script: script,
        });
      } catch (error) {
        console.error("Get script content error:", error);
        return createResponse(500, { error: "Failed to get script content" });
      }
    }

    // Update script content for admin
    if (path === "/admin/scripts/update-content" && method === "POST") {
      try {
        const adminUser = verifyAdminToken(event.headers.authorization);
        if (!adminUser) {
          return createResponse(401, { error: "Unauthorized" });
        }

        const body = JSON.parse(event.body || "{}");
        const { script_id, script_content } = body;

        if (!script_id || !script_content) {
          return createResponse(400, {
            error: "Missing script_id or script_content",
          });
        }

        const script = await database.getScript(script_id);
        if (!script) {
          return createResponse(404, { error: "Script not found" });
        }

        // Update script content in metadata
        await database.updateScriptStatus(script_id, {
          metadata: {
            ...script.metadata,
            script_content: script_content,
            last_updated: new Date().toISOString(),
          },
        });

        return createResponse(200, {
          message: "Script content updated successfully",
          script_id,
        });
      } catch (error) {
        console.error("Update script content error:", error);
        return createResponse(500, {
          error: "Failed to update script content",
        });
      }
    }

    // Serve uploaded scripts
    if (path.startsWith("/scripts/") && method === "GET") {
      try {
        const scriptId = path.replace("/scripts/", "").replace(".lua", "");

        if (!scriptId) {
          return createResponse(404, { error: "Script not found" });
        }

        const script = await database.getScript(scriptId);
        if (!script) {
          return createResponse(404, { error: "Script not found" });
        }

        // Get script content from metadata
        const scriptContent = script.metadata?.script_content;
        if (!scriptContent) {
          return createResponse(404, { error: "Script content not available" });
        }

        // Return the script content as plain text for loadstring
        return {
          statusCode: 200,
          headers: {
            "Content-Type": "text/plain; charset=utf-8",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Cache-Control": "no-cache",
          },
          body: scriptContent,
        };
      } catch (error) {
        console.error("Script serving error:", error);
        return createResponse(500, { error: "Failed to serve script" });
      }
    }

    return createResponse(404, { error: "Endpoint not found" });
  } catch (error) {
    console.error("Function error:", error);
    return createResponse(500, {
      error: "Internal server error",
      message: error.message,
    });
  }
};
