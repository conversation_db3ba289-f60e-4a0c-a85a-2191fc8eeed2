const crypto = require('crypto');
const { verifyRecaptcha, verifySecureToken } = require('../utils/auth');
const { getTokenData, updateTokenStep, checkRateLimit, cleanupExpiredTokens, isTokenValid } = require('../utils/database');
const { verifyLinkCompletion, createResponse, getClientIP, hashIP } = require('../utils/security');

async function verifyStep(event) {
  try {
    // Clean up expired tokens first
    await cleanupExpiredTokens();

    const { token, step, recaptchaResponse, verificationData } = JSON.parse(event.body);
    const ip = getClientIP(event);
    const ipHash = hashIP(ip);
    const sessionId = crypto.randomBytes(16).toString('hex');
    const rateLimitPassed = await checkRateLimit(sessionId, ipHash, 'link_verification');
    if (!rateLimitPassed) {

      return createResponse(429, { error: 'Too many verification attempts. Please wait before trying again.' });
    }

    const isValid = await verifyRecaptcha(recaptchaResponse);


    if (!isValid) {
      return createResponse(400, { error: 'Invalid captcha' });
    }

    const decodedToken = verifySecureToken(token);
    if (!decodedToken) {
      return createResponse(400, { error: 'Invalid or expired token' });
    }

    const tokenId = decodedToken.tokenId;

    // Double-check token validity in database
    const isTokenStillValid = await isTokenValid(tokenId);
    if (!isTokenStillValid) {
      return createResponse(400, { error: 'Token has expired. Please start over.' });
    }

    const { data: tokenData, error } = await getTokenData(tokenId);

    if (error) {

      return createResponse(400, { error: 'Invalid token' });
    }

    if (!tokenData) {

      return createResponse(400, { error: 'Invalid token' });
    }

    if (new Date(tokenData.expires_at) < new Date()) {
      return createResponse(400, { error: 'Token has expired. Please start over.' });
    }

    if (step === 2 && tokenData.current_step !== 1 && tokenData.current_step !== 2) {
      return createResponse(400, { error: 'Invalid step progression. Please complete steps in order.' });
    }

    if (step === 3 && tokenData.current_step !== 2 && tokenData.current_step !== 3) {
      return createResponse(400, { error: 'Invalid step progression. Please complete steps in order.' });
    }

    if (step < tokenData.current_step) {
      return createResponse(200, { message: 'Step already completed', verified: true });
    }

    if (step === 2 || step === 3) {
      const ip = getClientIP(event);
      const ipHash = hashIP(ip);
      const userAgent = event.headers['user-agent'] || '';
      const referrer = event.headers['referer'] || event.headers['referrer'] || '';

      const selectedService = verificationData?.service || tokenData.selected_service || 'linkvertise';

      const linkVerificationData = {
        referrer,
        userAgent,
        ipHash,
        timeSpent: verificationData?.timeSpent || 0,
        mouseInteractions: verificationData?.mouseInteractions || 0,
        screenWidth: verificationData?.screenWidth || 0,
        screenHeight: verificationData?.screenHeight || 0,
        hasValidReferrer: false
      };

      const linkVerification = await verifyLinkCompletion(tokenId, selectedService, linkVerificationData);

      if (!linkVerification.valid) {

        return createResponse(400, { 
          error: linkVerification.reason,
          bypassDetected: linkVerification.reason.includes('Bypass') || linkVerification.reason.includes('bypass')
        });
      }


    }

    const updateData = {};


    if (step === 2 || step === 3) {
      const ip = getClientIP(event);
      const ipHash = hashIP(ip);
      updateData.ip_hash = ipHash;
    }

    if (step === 2 && verificationData) {
      updateData.verification_metadata = JSON.stringify({
        referrer: event.headers['referer'] || event.headers['referrer'] || '',
        userAgent: event.headers['user-agent'] || '',
        timeSpent: verificationData.timeSpent || 0,
        mouseInteractions: verificationData.mouseInteractions || 0,
        verifiedAt: new Date().toISOString()
      });
    }

    if (step === 3 && verificationData) {
      updateData.verification_metadata = JSON.stringify({
        referrer: event.headers['referer'] || event.headers['referrer'] || '',
        userAgent: event.headers['user-agent'] || '',
        timeSpent: verificationData.timeSpent || 0,
        mouseInteractions: verificationData.mouseInteractions || 0,
        finalVerifiedAt: new Date().toISOString()
      });
    }

    let nextStep = step;
    if (step === 2 && tokenData.current_step === 2) {
      nextStep = 3;
    } else if (step === tokenData.current_step) {
      nextStep = step + 1;
    }

    const { data: updateResult, error: updateError } = await updateTokenStep(tokenId, nextStep, updateData);

    if (updateError) {

      return createResponse(500, { error: 'Failed to update step' });
    }



    return createResponse(200, { 
      message: 'Step verified',
      verified: true
    });
  } catch (error) {

    return createResponse(500, { error: 'Server error. Please try again.' });
  }
}

module.exports = {
  verifyStep
};
