const fetch = require('node-fetch');
const { createResponse, getClientIP, hashIP } = require('../utils/security');
const { verifySecureToken } = require('../utils/auth');
const { getTokenData, updateTokenStep } = require('../utils/database');
const crypto = require('crypto');

// Configuration
const LINKVERTISE_API_URL = 'https://publisher.linkvertise.com/api/v1/token/validate';
const LINKVERTISE_API_KEY = process.env.LINKVERTISE_API_KEY || '';
const LINKVERTISE_USER_ID = process.env.LINKVERTISE_USER_ID || '1028057'; // Your Linkvertise user ID

/**
 * Validates a token from Linkvertise to verify if the user completed the link shortener
 *
 * @param {Object} event - The Netlify function event
 * @returns {Object} Response object
 */
async function verifyLinkvertiseToken(event) {
  try {
    const { token, progressionToken } = JSON.parse(event.body);

    if (!token) {
      return createResponse(400, { error: 'Linkvertise token is required' });
    }

    if (!progressionToken) {
      return createResponse(400, { error: 'Progression token is required' });
    }

    // Verify our progression token
    const decodedToken = verifySecureToken(progressionToken);
    if (!decodedToken) {
      return createResponse(400, { error: 'Invalid or expired progression token' });
    }

    const tokenId = decodedToken.tokenId;
    const { data: tokenData, error } = await getTokenData(tokenId);

    if (error || !tokenData) {
      return createResponse(400, { error: 'Invalid token data' });
    }

    // Validate the token with Linkvertise API
    const lvValidation = await validateWithLinkvertise(token);

    if (!lvValidation.success) {
      return createResponse(400, {
        error: 'Invalid Linkvertise token',
        details: lvValidation.message || 'The token could not be verified'
      });
    }

    // Update token to indicate Linkvertise verification
    const ip = getClientIP(event);
    const ipHash = hashIP(ip);

    const updateData = {
      lv_token_verified: true,
      ip_hash: ipHash,
      verification_metadata: JSON.stringify({
        ...JSON.parse(tokenData.verification_metadata || '{}'),
        linkvertise_verified: true,
        linkvertise_verification_time: new Date().toISOString(),
        linkvertise_token: token.substring(0, 10) + '...' // Only store part of the token for security
      })
    };

    // Mark the token as verified in our system
    const { data: updateResult, error: updateError } = await updateTokenStep(
      tokenId,
      tokenData.current_step,
      updateData
    );

    if (updateError) {
      console.error('Error updating token with Linkvertise verification:', updateError);
      return createResponse(500, { error: 'Failed to update verification status' });
    }

    // Create a HMAC of the token for verification on client
    const tokenHmac = crypto
      .createHmac('sha256', process.env.JWT_SECRET || 'fallback-secret')
      .update(token)
      .digest('hex');

    return createResponse(200, {
      verified: true,
      message: 'Linkvertise token verified successfully',
      verification_id: tokenHmac
    });

  } catch (error) {
    console.error('Linkvertise token verification error:', error);
    return createResponse(500, { error: 'Server error verifying Linkvertise token' });
  }
}

/**
 * Validates a token with the Linkvertise API
 *
 * @param {string} token - The Linkvertise token to validate
 * @returns {Object} Validation result
 */
async function validateWithLinkvertise(token) {
  try {
    if (!LINKVERTISE_API_KEY) {
      console.warn('LINKVERTISE_API_KEY not configured, falling back to token format validation');

      // Fallback validation if API key is not available
      // This is a simple check to see if the token looks valid
      // (should be replaced with actual API validation in production)
      const isValidFormat = typeof token === 'string' &&
                           token.length >= 20 &&
                           /^[a-zA-Z0-9_-]+$/.test(token);

      return {
        success: isValidFormat,
        message: isValidFormat ? 'Token format valid (API key not configured)' : 'Invalid token format'
      };
    }

    // Make request to Linkvertise API
    const response = await fetch(LINKVERTISE_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Linkvertise-API-Key': LINKVERTISE_API_KEY
      },
      body: JSON.stringify({
        user_id: LINKVERTISE_USER_ID,
        token: token
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Linkvertise API error:', errorText);
      return {
        success: false,
        message: `API error (${response.status}): ${errorText}`
      };
    }

    const data = await response.json();

    return {
      success: data.valid === true,
      message: data.message || (data.valid ? 'Token valid' : 'Invalid token'),
      data: data
    };

  } catch (error) {
    console.error('Error validating with Linkvertise:', error);
    return {
      success: false,
      message: 'Error validating token with Linkvertise'
    };
  }
}

module.exports = {
  verifyLinkvertiseToken
};
