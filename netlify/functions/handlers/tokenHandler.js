const crypto = require('crypto');
const { verifyRecaptcha, generateSecureToken } = require('../utils/auth');
const { storeToken, checkRateLimit, cleanupExpiredTokens } = require('../utils/database');
const { createResponse, getClientIP, hashIP } = require('../utils/security');

async function generateToken(event) {
  try {
    const { recaptchaResponse } = JSON.parse(event.body);


    const ip = getClientIP(event);
    const ipHash = hashIP(ip);
    const sessionId = crypto.randomBytes(16).toString('hex');


    const rateLimitPassed = await checkRateLimit(sessionId, ipHash, 'token_generation');
    if (!rateLimitPassed) {

      return createResponse(429, { error: 'Too many token generation attempts. Please wait before trying again.' });
    }

    const isValid = await verifyRecaptcha(recaptchaResponse);


    if (!isValid) {
      return createResponse(400, { error: 'Invalid captcha. Please try again.' });
    }
    
    const tokenId = crypto.randomBytes(16).toString('hex');
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 30);
    
    const payload = {
      tokenId,
      current_step: 1,
      created_at: new Date().toISOString(),
      expires_at: expiresAt.toISOString()
    };
    
    const token = generateSecureToken(payload);
    
    const { data, error: dbError } = await storeToken(tokenId, expiresAt, ipHash);
    
    if (dbError) {

      return createResponse(500, { error: 'Failed to store token. Please try again.' });
    }
    

    
    return createResponse(200, { token });
  } catch (error) {

    return createResponse(500, { error: 'Server error. Please try again.' });
  }
}

async function trackLinkVisit(event) {
  try {
    const { verifySecureToken } = require('../utils/auth');
    const { trackLinkVisit: dbTrackLinkVisit } = require('../utils/database');
    
    const { token, service } = JSON.parse(event.body);
    
    const decodedToken = verifySecureToken(token);
    if (!decodedToken) {
      return createResponse(400, { error: 'Invalid or expired token' });
    }
    
    const tokenId = decodedToken.tokenId;
    
    const { error: updateError } = await dbTrackLinkVisit(tokenId, service);
    
    if (updateError) {

      return createResponse(500, { error: 'Failed to track visit' });
    }
    

    
    return createResponse(200, { message: 'Visit tracked' });
  } catch (error) {

    return createResponse(500, { error: 'Server error. Please try again.' });
  }
}

module.exports = {
  generateToken,
  trackLinkVisit
};
