const crypto = require('crypto');
const { verifyRecapt<PERSON>, verifyS<PERSON>ureToken, generateKey } = require('../utils/auth');
const { database } = require('../utils/database');
const { createResponse, getClientIP, hashIP } = require('../utils/security');

// Enhanced key generation with better format
function generateAdvancedKey() {
    // Generate a more professional key format: PL-XXXX-XXXX-XXXX
    const segments = [];
    for (let i = 0; i < 3; i++) {
        const segment = crypto.randomBytes(2).toString('hex').toUpperCase();
        segments.push(segment);
    }

    // Add a checksum for validation
    const keyBase = segments.join('');
    const checksum = crypto.createHash('md5').update(keyBase + 'PROJECT_L_SALT').digest('hex').substring(0, 2).toUpperCase();
    segments.push(checksum);

    return `PL-${segments.join('-')}`;
}

// Validate key format
function validateKeyFormat(key) {
    if (!key || typeof key !== 'string') {
        return false;
    }

    // Check basic format: PL-XXXX-XXXX-XXXX-XX
    const keyPattern = /^PL-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{2}$/;
    if (!keyPattern.test(key)) {
        return false;
    }

    // Validate checksum
    const parts = key.split('-');
    const segments = parts.slice(1, 4); // Get first 3 segments
    const providedChecksum = parts[4];

    const keyBase = segments.join('');
    const expectedChecksum = crypto.createHash('md5').update(keyBase + 'PROJECT_L_SALT').digest('hex').substring(0, 2).toUpperCase();

    return providedChecksum === expectedChecksum;
}

async function generateKeyHandler(event) {
    try {
        // Clean up expired sessions first
        await database.cleanupExpiredSessions();

        const { token, recaptchaResponse, skipRecaptcha } = JSON.parse(event.body);
        const ip = getClientIP(event);
        const ipHash = hashIP(ip);
        const sessionId = crypto.randomBytes(16).toString('hex');

        // Rate limiting check
        const rateLimitPassed = await database.checkRateLimit(`${ipHash}:key_gen`, 'key_generation', 3, 1440); // 3 per day
        if (!rateLimitPassed) {
            return createResponse(429, {
                error: 'Daily key generation limit reached. You can generate 3 keys per day.',
                retryAfter: 86400 // 24 hours
            });
        }

        // Verify reCAPTCHA unless skipped
        if (!skipRecaptcha) {
            const isValid = await verifyRecaptcha(recaptchaResponse);
            if (!isValid) {
                return createResponse(400, { error: 'Invalid captcha verification' });
            }
        }

        // Check if user already generated a key today
        const alreadyGeneratedToday = await database.hasGeneratedKeyToday(ipHash);
        if (alreadyGeneratedToday) {
            // Get today's key instead of generating new one
            const todayKey = await getTodaysKeyHandler(event);
            if (todayKey.statusCode === 200) {
                const keyData = JSON.parse(todayKey.body);
                return createResponse(200, {
                    ...keyData,
                    message: 'Returning your existing key for today'
                });
            }
        }

        // Verify session token
        const decodedToken = verifySecureToken(token);
        if (!decodedToken) {
            return createResponse(400, { error: 'Invalid or expired session token' });
        }

        const tokenId = decodedToken.tokenId;

        // Check session validity in database
        const sessionData = await database.getSession(tokenId);
        if (!sessionData) {
            return createResponse(400, { error: 'Session not found or expired' });
        }

        // Verify all verification steps are completed
        if (sessionData.current_step < 3) {
            return createResponse(400, {
                error: 'Please complete all verification steps first',
                currentStep: sessionData.current_step,
                requiredStep: 3
            });
        }

        // Generate the new key
        const keyCode = generateAdvancedKey();

        // Set expiration (24 hours from now)
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 24);

        // Store key in database
        const keyData = await database.createKey({
            key: keyCode,
            sessionId: sessionId,
            ipHash: ipHash,
            expiresAt: expiresAt.toISOString(),
            createdBy: 'user',
            metadata: {
                sessionToken: tokenId,
                userAgent: event.headers['user-agent'] || '',
                generationMethod: 'web_interface',
                verificationSteps: sessionData.completed_steps || [1, 2, 3]
            }
        });

        // Clean up the session token (one-time use)
        await database.deleteSession(tokenId);

        // Log successful key generation
        console.log(`Key generated successfully: ${keyCode.substring(0, 8)}... for IP: ${ipHash.substring(0, 8)}...`);

        return createResponse(200, {
            key: keyCode,
            expiresAt: expiresAt.toISOString(),
            format: 'PL-XXXX-XXXX-XXXX-XX',
            usage: 'Use this key to access Project L services',
            message: 'Key generated successfully! Save it securely.',
            metadata: {
                id: keyData.id,
                createdAt: keyData.created_at,
                status: keyData.status
            }
        });

    } catch (error) {
        console.error('Key generation error:', error);
        return createResponse(500, {
            error: 'Key generation failed. Please try again.',
            code: 'KEY_GEN_ERROR'
        });
    }
}

async function validateKey(event) {
    try {
        const { key, hwid } = JSON.parse(event.body);

        if (!key || !hwid) {
            return createResponse(400, {
                error: 'Missing required parameters',
                required: ['key', 'hwid']
            });
        }

        // Validate key format first
        if (!validateKeyFormat(key)) {
            return createResponse(400, {
                error: 'Invalid key format',
                expectedFormat: 'PL-XXXX-XXXX-XXXX-XX'
            });
        }

        // Validate HWID format
        if (hwid.length > 100 || !/^[a-zA-Z0-9\-_]+$/.test(hwid)) {
            return createResponse(400, {
                error: 'Invalid HWID format',
                maxLength: 100,
                allowedChars: 'alphanumeric, hyphens, underscores'
            });
        }

        // Check key in database and handle HWID binding
        const validation = await database.validateAndUseKey(key, hwid);

        if (!validation.valid) {
            // Log failed validation attempt
            console.log(`Key validation failed: ${key.substring(0, 8)}... - ${validation.reason}`);

            return createResponse(400, {
                error: validation.reason,
                valid: false,
                code: 'VALIDATION_FAILED'
            });
        }

        // Get updated key data
        const keyData = await database.getKey(key);

        return createResponse(200, {
            valid: true,
            message: validation.reason,
            keyInfo: {
                status: keyData.status,
                expiresAt: keyData.expires_at,
                createdAt: keyData.created_at,
                usageCount: keyData.usage_count,
                boundDevice: !!keyData.hwid
            }
        });

    } catch (error) {
        console.error('Key validation error:', error);
        return createResponse(500, {
            error: 'Key validation failed. Please try again.',
            code: 'VALIDATION_ERROR'
        });
    }
}

async function resetHWID(event) {
    try {
        const { key } = JSON.parse(event.body);

        if (!key) {
            return createResponse(400, {
                error: 'Key is required',
                parameter: 'key'
            });
        }

        // Validate key format
        if (!validateKeyFormat(key)) {
            return createResponse(400, {
                error: 'Invalid key format',
                expectedFormat: 'PL-XXXX-XXXX-XXXX-XX'
            });
        }

        // Get key data
        const keyData = await database.getKey(key);
        if (!keyData) {
            return createResponse(404, {
                error: 'Key not found',
                code: 'KEY_NOT_FOUND'
            });
        }

        // Check if key is expired
        if (new Date(keyData.expires_at) < new Date()) {
            return createResponse(400, {
                error: 'Cannot reset HWID for expired key',
                expiresAt: keyData.expires_at
            });
        }

        // Reset HWID
        await database.updateKey(key, {
            hwid: null,
            metadata: {
                ...keyData.metadata,
                hwidResetAt: new Date().toISOString(),
                hwidResetCount: (keyData.metadata?.hwidResetCount || 0) + 1
            }
        });

        console.log(`HWID reset for key: ${key.substring(0, 8)}...`);

        return createResponse(200, {
            message: 'HWID reset successfully. You can now use this key on a different device.',
            resetAt: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('HWID reset error:', error);
        return createResponse(500, { 
            error: 'HWID reset failed. Please try again.',
            code: 'HWID_RESET_ERROR'
        });
    }
}

async function checkKeyStatus(event) {
    try {
        const { key } = JSON.parse(event.body);
        
        if (!key) {
            return createResponse(400, { 
                error: 'Key is required',
                parameter: 'key'
            });
        }
        
        // Validate key format
        if (!validateKeyFormat(key)) {
            return createResponse(400, { 
                error: 'Invalid key format',
                expectedFormat: 'PL-XXXX-XXXX-XXXX-XX'
            });
        }
        
        // Get key data
        const keyData = await database.getKey(key);
        if (!keyData) {
            return createResponse(404, { 
                error: 'Key not found',
                code: 'KEY_NOT_FOUND'
            });
        }
        
        const now = new Date();
        const expiresAt = new Date(keyData.expires_at);
        const isExpired = expiresAt < now;
        const timeUntilExpiry = Math.max(0, expiresAt - now);
        
        return createResponse(200, { 
            status: isExpired ? 'expired' : keyData.status,
            valid: !isExpired && keyData.status === 'active',
            keyInfo: {
                createdAt: keyData.created_at,
                expiresAt: keyData.expires_at,
                isExpired: isExpired,
                timeUntilExpiry: timeUntilExpiry,
                usageCount: keyData.usage_count || 0,
                lastUsed: keyData.last_used_at,
                boundToDevice: !!keyData.hwid,
                createdBy: keyData.created_by
            }
        });
        
    } catch (error) {
        console.error('Key status check error:', error);
        return createResponse(500, { 
            error: 'Status check failed. Please try again.',
            code: 'STATUS_CHECK_ERROR'
        });
    }
}

async function canGenerateKey(event) {
    try {
        const ip = getClientIP(event);
        const ipHash = hashIP(ip);
        
        // Check if user has generated a key today
        const alreadyGeneratedToday = await database.hasGeneratedKeyToday(ipHash);
        
        // Check rate limit status
        const rateLimitPassed = await database.checkRateLimit(`${ipHash}:key_gen_check`, 'rate_check', 10, 1); // 10 checks per minute
        
        if (!rateLimitPassed) {
            return createResponse(429, { 
                error: 'Too many requests. Please wait before checking again.',
                retryAfter: 60
            });
        }
        
        if (alreadyGeneratedToday) {
            return createResponse(200, { 
                canGenerate: false,
                reason: 'Daily limit reached',
                message: 'You have already generated a key today. Please try again tomorrow.',
                nextAvailable: new Date(new Date().setHours(24, 0, 0, 0)).toISOString()
            });
        }
        
        return createResponse(200, { 
            canGenerate: true,
            message: 'You can generate a new key today.',
            remainingGenerations: 1,
            dailyLimit: 1
        });
        
    } catch (error) {
        console.error('Can generate key check error:', error);
        return createResponse(500, { 
            error: 'Generation check failed. Please try again.',
            code: 'GEN_CHECK_ERROR'
        });
    }
}

async function getTodaysKeyHandler(event) {
    try {
        const ip = getClientIP(event);
        const ipHash = hashIP(ip);
        
        // Get today's date range
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // Find key generated today for this IP
        const { keys } = await database.getKeys({
            search: '',
            dateFrom: today.toISOString(),
            limit: 1
        });
        
        const todayKey = keys.find(key => key.ip_hash === ipHash);
        
        if (!todayKey) {
            return createResponse(404, { 
                error: 'No key found for today',
                message: 'You have not generated a key today yet.',
                canGenerate: true
            });
        }
        
        // Check if key is expired
        const isExpired = new Date(todayKey.expires_at) < new Date();
        
        if (isExpired) {
            return createResponse(400, { 
                error: 'Today\'s key has expired',
                message: 'Your key for today has expired. Please generate a new one.',
                expiredAt: todayKey.expires_at
            });
        }
        
        return createResponse(200, { 
            key: todayKey.key_code,
            expiresAt: todayKey.expires_at,
            createdAt: todayKey.created_at,
            status: todayKey.status,
            usageCount: todayKey.usage_count || 0,
            message: 'Here is your key for today'
        });
        
    } catch (error) {
        console.error('Get today\'s key error:', error);
        return createResponse(500, { 
            error: 'Failed to retrieve today\'s key. Please try again.',
            code: 'TODAY_KEY_ERROR'
        });
    }
}

// Simple key check for external API calls (Lua scripts, etc.)
async function simpleKeyCheck(key, hwid) {
    try {
        if (!validateKeyFormat(key)) {
            return { valid: false, message: 'Invalid key format' };
        }
        
        const validation = await database.validateAndUseKey(key, hwid);
        
        return {
            valid: validation.valid,
            message: validation.reason
        };
        
    } catch (error) {
        console.error('Simple key check error:', error);
        return { valid: false, message: 'Validation error' };
    }
}

module.exports = {
    generateKeyHandler,
    validateKey,
    resetHWID,
    checkKeyStatus,
    canGenerateKey,
    getTodaysKeyHandler,
    simpleKeyCheck,
    generateAdvancedKey,
    validateKeyFormat
};